"""
用户服务测试
"""
import pytest
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession

from app.application.services.user_service import UserService
from app.domain.schemas.user import UserCreate, UserUpdate, UserProfile
from app.domain.models.user import User as UserModel
from app.core.exceptions import AppException


class TestUserService:
    """用户服务测试类"""
    
    @pytest.fixture
    def user_service(self, db_session: AsyncSession) -> UserService:
        """创建用户服务实例"""
        return UserService(db_session)
    
    @pytest.mark.asyncio
    async def test_get_user_by_id_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试根据ID获取用户成功"""
        user = await user_service.get_user_by_id(test_user.id)
        
        assert user is not None
        assert user.id == test_user.id
        assert user.email == test_user.email
        assert user.username == test_user.username
    
    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(self, user_service: UserService):
        """测试根据ID获取用户 - 用户不存在"""
        fake_id = uuid4()
        user = await user_service.get_user_by_id(fake_id)
        
        assert user is None
    
    @pytest.mark.asyncio
    async def test_get_user_by_email_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试根据邮箱获取用户成功"""
        user = await user_service.get_user_by_email(test_user.email)
        
        assert user is not None
        assert user.email == test_user.email
        assert user.id == test_user.id
    
    @pytest.mark.asyncio
    async def test_get_user_by_email_not_found(self, user_service: UserService):
        """测试根据邮箱获取用户 - 用户不存在"""
        user = await user_service.get_user_by_email("<EMAIL>")
        
        assert user is None
    
    @pytest.mark.asyncio
    async def test_get_user_by_username_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试根据用户名获取用户成功"""
        user = await user_service.get_user_by_username(test_user.username)
        
        assert user is not None
        assert user.username == test_user.username
        assert user.id == test_user.id
    
    @pytest.mark.asyncio
    async def test_create_user_success(
        self, 
        user_service: UserService, 
        test_data_generator
    ):
        """测试创建用户成功"""
        user_data = test_data_generator.generate_user_data()
        user_create = UserCreate(**user_data)
        
        created_user = await user_service.create_user(user_create)
        
        assert created_user.email == user_data["email"]
        assert created_user.username == user_data["username"]
        assert created_user.full_name == user_data["full_name"]
        assert created_user.is_active is True
        assert created_user.is_superuser is False
        assert created_user.id is not None
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(
        self, 
        user_service: UserService, 
        test_user: UserModel,
        test_data_generator
    ):
        """测试创建用户失败 - 邮箱已存在"""
        user_data = test_data_generator.generate_user_data()
        user_data["email"] = test_user.email  # 使用已存在的邮箱
        
        user_create = UserCreate(**user_data)
        
        with pytest.raises(AppException) as exc_info:
            await user_service.create_user(user_create)
        
        assert exc_info.value.error_code == "EMAIL_ALREADY_EXISTS"
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_username(
        self, 
        user_service: UserService, 
        test_user: UserModel,
        test_data_generator
    ):
        """测试创建用户失败 - 用户名已存在"""
        user_data = test_data_generator.generate_user_data()
        user_data["username"] = test_user.username  # 使用已存在的用户名
        
        user_create = UserCreate(**user_data)
        
        with pytest.raises(AppException) as exc_info:
            await user_service.create_user(user_create)
        
        assert exc_info.value.error_code == "USERNAME_ALREADY_EXISTS"
    
    @pytest.mark.asyncio
    async def test_update_user_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试更新用户成功"""
        update_data = UserUpdate(
            full_name="Updated Full Name",
            is_active=False
        )
        
        updated_user = await user_service.update_user(test_user.id, update_data)
        
        assert updated_user.full_name == "Updated Full Name"
        assert updated_user.is_active is False
        assert updated_user.email == test_user.email  # 未更新的字段保持不变
    
    @pytest.mark.asyncio
    async def test_update_user_not_found(self, user_service: UserService):
        """测试更新用户失败 - 用户不存在"""
        fake_id = uuid4()
        update_data = UserUpdate(full_name="New Name")
        
        with pytest.raises(AppException) as exc_info:
            await user_service.update_user(fake_id, update_data)
        
        assert exc_info.value.error_code == "USER_NOT_FOUND"
    
    @pytest.mark.asyncio
    async def test_update_user_profile_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试更新用户个人资料成功"""
        profile_data = UserProfile(
            full_name="Updated Profile Name",
            phone="+1234567890",
            bio="Updated bio"
        )
        
        updated_user = await user_service.update_user_profile(test_user.id, profile_data)
        
        assert updated_user.full_name == "Updated Profile Name"
        # 注意：phone和bio字段需要在User模型中定义
    
    @pytest.mark.asyncio
    async def test_delete_user_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试删除用户成功"""
        result = await user_service.delete_user(test_user.id)
        
        assert result is True
        
        # 验证用户已被删除
        deleted_user = await user_service.get_user_by_id(test_user.id)
        assert deleted_user is None
    
    @pytest.mark.asyncio
    async def test_delete_user_not_found(self, user_service: UserService):
        """测试删除用户失败 - 用户不存在"""
        fake_id = uuid4()
        result = await user_service.delete_user(fake_id)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_activate_user_success(
        self, 
        user_service: UserService, 
        db_session: AsyncSession
    ):
        """测试激活用户成功"""
        # 创建一个未激活的用户
        from app.core.security import get_password_hash
        
        inactive_user = UserModel(
            email="<EMAIL>",
            username="inactive",
            full_name="Inactive User",
            hashed_password=get_password_hash("password"),
            is_active=False
        )
        
        db_session.add(inactive_user)
        await db_session.commit()
        await db_session.refresh(inactive_user)
        
        # 激活用户
        activated_user = await user_service.activate_user(inactive_user.id)
        
        assert activated_user is not None
        assert activated_user.is_active is True
    
    @pytest.mark.asyncio
    async def test_deactivate_user_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试停用用户成功"""
        deactivated_user = await user_service.deactivate_user(test_user.id)
        
        assert deactivated_user is not None
        assert deactivated_user.is_active is False
    
    @pytest.mark.asyncio
    async def test_get_users_success(
        self, 
        user_service: UserService, 
        test_user: UserModel,
        test_admin_user: UserModel
    ):
        """测试获取用户列表成功"""
        users, total = await user_service.get_users(
            offset=0,
            limit=10
        )
        
        assert len(users) >= 2  # 至少有测试用户和管理员用户
        assert total >= 2
        assert any(user.id == test_user.id for user in users)
        assert any(user.id == test_admin_user.id for user in users)
    
    @pytest.mark.asyncio
    async def test_get_users_with_search(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试搜索用户"""
        users, total = await user_service.get_users(
            search=test_user.username,
            offset=0,
            limit=10
        )
        
        assert len(users) >= 1
        assert any(user.id == test_user.id for user in users)
    
    @pytest.mark.asyncio
    async def test_get_users_with_filters(
        self, 
        user_service: UserService, 
        test_user: UserModel,
        test_admin_user: UserModel
    ):
        """测试过滤用户"""
        # 测试按活跃状态过滤
        active_users, active_total = await user_service.get_users(
            is_active=True,
            offset=0,
            limit=10
        )
        
        assert all(user.is_active for user in active_users)
        
        # 测试按角色过滤
        admin_users, admin_total = await user_service.get_users(
            role="admin",
            offset=0,
            limit=10
        )
        
        assert all(user.is_superuser for user in admin_users)
    
    @pytest.mark.asyncio
    async def test_change_password_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试修改密码成功"""
        new_password = "newpassword123"
        result = await user_service.change_password(test_user.id, new_password)
        
        assert result is True
        
        # 验证新密码
        from app.core.security import verify_password
        updated_user_model = await user_service.user_repository.get_by_email(test_user.email)
        assert verify_password(new_password, updated_user_model.hashed_password)
    
    @pytest.mark.asyncio
    async def test_change_password_user_not_found(self, user_service: UserService):
        """测试修改密码失败 - 用户不存在"""
        fake_id = uuid4()
        result = await user_service.change_password(fake_id, "newpassword")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_verify_password_success(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试验证密码成功"""
        result = await user_service.verify_password(test_user.id, "testpassword")
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_verify_password_wrong_password(
        self, 
        user_service: UserService, 
        test_user: UserModel
    ):
        """测试验证密码失败 - 密码错误"""
        result = await user_service.verify_password(test_user.id, "wrongpassword")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_verify_password_user_not_found(self, user_service: UserService):
        """测试验证密码失败 - 用户不存在"""
        fake_id = uuid4()
        result = await user_service.verify_password(fake_id, "password")
        
        assert result is False
