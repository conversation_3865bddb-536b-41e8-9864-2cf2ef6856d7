"""
数据收集服务
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import structlog

from app.core.config import settings
from app.core.database import get_db
from app.core.exceptions import AppException
from app.infrastructure.external.coingecko_client import CoinGeckoClient
from app.infrastructure.repositories.usdt_repository import USDTDataRepository
from app.domain.schemas.usdt import USDTDataCreate

logger = structlog.get_logger()


class DataCollector:
    """数据收集器"""
    
    def __init__(self):
        self.coingecko_client = CoinGeckoClient()
        self.is_running = False
        self.collection_interval = settings.MONITORING_INTERVAL
        self.last_collection_time: Optional[datetime] = None
        self.collection_stats = {
            'total_collections': 0,
            'successful_collections': 0,
            'failed_collections': 0,
            'last_error': None
        }
    
    async def start_collection(self):
        """开始数据收集"""
        if self.is_running:
            logger.warning("Data collection is already running")
            return
        
        self.is_running = True
        logger.info("Starting USDT data collection", interval=self.collection_interval)
        
        try:
            await self.coingecko_client.connect()
            
            while self.is_running:
                try:
                    await self.collect_usdt_data()
                    self.collection_stats['successful_collections'] += 1
                    
                    # 等待下一次收集
                    await asyncio.sleep(self.collection_interval)
                    
                except Exception as e:
                    self.collection_stats['failed_collections'] += 1
                    self.collection_stats['last_error'] = str(e)
                    
                    logger.error(
                        "Data collection failed",
                        error=str(e),
                        collection_count=self.collection_stats['total_collections']
                    )
                    
                    # 失败后等待较短时间再重试
                    await asyncio.sleep(min(self.collection_interval, 60))
                
                finally:
                    self.collection_stats['total_collections'] += 1
        
        finally:
            await self.coingecko_client.disconnect()
            logger.info("Data collection stopped")
    
    async def stop_collection(self):
        """停止数据收集"""
        self.is_running = False
        logger.info("Stopping data collection")
    
    async def collect_usdt_data(self) -> Optional[Dict[str, Any]]:
        """收集USDT数据"""
        try:
            # 从CoinGecko获取数据
            raw_data = await self.coingecko_client.get_usdt_data()
            
            # 数据验证和处理
            processed_data = await self._process_raw_data(raw_data)
            
            # 异常检测
            is_anomaly = await self._detect_anomaly(processed_data)
            processed_data['is_anomaly'] = is_anomaly
            
            # 保存到数据库
            async with get_db() as db:
                repository = USDTDataRepository(db)
                
                usdt_data_create = USDTDataCreate(**processed_data)
                saved_data = await repository.create(obj_in=usdt_data_create)
                
                logger.info(
                    "USDT data collected successfully",
                    price=float(processed_data['current_price_usd']),
                    timestamp=processed_data['timestamp'],
                    is_anomaly=is_anomaly,
                    data_id=str(saved_data.id)
                )
            
            self.last_collection_time = datetime.utcnow()
            return processed_data
            
        except Exception as e:
            logger.error("Failed to collect USDT data", error=str(e))
            raise AppException(
                error_code="DATA_COLLECTION_ERROR",
                message="数据收集失败",
                details={"error": str(e)}
            )
    
    async def _process_raw_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理原始数据"""
        try:
            # 基本数据验证
            if not raw_data.get('current_price_usd'):
                raise ValueError("Missing current_price_usd")
            
            # 价格精度处理
            price = raw_data['current_price_usd']
            price_precision = f"{price:.10f}"  # 保持10位小数精度
            
            # 构建处理后的数据
            processed_data = {
                'timestamp': raw_data.get('timestamp', datetime.utcnow()),
                'unix_timestamp': raw_data.get('unix_timestamp', int(datetime.utcnow().timestamp())),
                'name': raw_data.get('name', 'Tether'),
                'symbol': raw_data.get('symbol', 'USDT'),
                'current_price_usd': price,
                'price_precision': price_precision,
                'data_source': raw_data.get('data_source', 'coingecko'),
                'source_id': raw_data.get('source_id'),
                'last_updated': raw_data.get('last_updated', datetime.utcnow()),
                
                # 市场数据
                'market_cap_usd': raw_data.get('market_cap_usd'),
                'market_cap_rank': raw_data.get('market_cap_rank'),
                'total_volume_usd': raw_data.get('total_volume_usd'),
                'circulating_supply': raw_data.get('circulating_supply'),
                'total_supply': raw_data.get('total_supply'),
                'max_supply': raw_data.get('max_supply'),
                
                # 价格变化数据
                'price_change_24h': raw_data.get('price_change_24h'),
                'price_change_percentage_24h': raw_data.get('price_change_percentage_24h'),
                'price_change_7d': raw_data.get('price_change_7d'),
                'price_change_percentage_7d': raw_data.get('price_change_percentage_7d'),
                'price_change_30d': raw_data.get('price_change_30d'),
                'price_change_percentage_30d': raw_data.get('price_change_percentage_30d'),
                
                # 市值变化数据
                'market_cap_change_24h': raw_data.get('market_cap_change_24h'),
                'market_cap_change_percentage_24h': raw_data.get('market_cap_change_percentage_24h'),
                
                # 交易量数据
                'volume_24h': raw_data.get('volume_24h'),
                'volume_change_24h': raw_data.get('volume_change_24h'),
                
                # 技术指标
                'ath': raw_data.get('ath'),
                'ath_date': raw_data.get('ath_date'),
                'atl': raw_data.get('atl'),
                'atl_date': raw_data.get('atl_date'),
                
                # 数据质量
                'confidence_score': raw_data.get('confidence_score', 0.8),
                'is_anomaly': False  # 将在异常检测中设置
            }
            
            return processed_data
            
        except Exception as e:
            logger.error("Failed to process raw data", error=str(e), raw_data=raw_data)
            raise AppException(
                error_code="DATA_PROCESSING_ERROR",
                message="数据处理失败",
                details={"error": str(e)}
            )
    
    async def _detect_anomaly(self, data: Dict[str, Any]) -> bool:
        """异常检测"""
        try:
            price = float(data['current_price_usd'])
            
            # 基本异常检测规则
            anomaly_conditions = [
                # 价格异常偏离1美元
                abs(price - 1.0) > 0.05,  # 偏离超过5%
                
                # 价格为0或负数
                price <= 0,
                
                # 价格过高（不太可能）
                price > 2.0,
                
                # 24小时价格变化过大
                abs(data.get('price_change_percentage_24h', 0)) > 10,  # 24小时变化超过10%
            ]
            
            # 检查是否满足任何异常条件
            is_anomaly = any(anomaly_conditions)
            
            if is_anomaly:
                logger.warning(
                    "Anomaly detected in USDT data",
                    price=price,
                    price_change_24h=data.get('price_change_percentage_24h'),
                    timestamp=data['timestamp']
                )
            
            return is_anomaly
            
        except Exception as e:
            logger.error("Anomaly detection failed", error=str(e))
            return False  # 检测失败时不标记为异常
    
    async def collect_historical_data(
        self,
        days: int = 7,
        interval: str = 'hourly'
    ) -> List[Dict[str, Any]]:
        """收集历史数据"""
        try:
            logger.info("Starting historical data collection", days=days, interval=interval)
            
            # 从CoinGecko获取历史数据
            historical_data = await self.coingecko_client.get_usdt_history(days, interval)
            
            collected_data = []
            async with get_db() as db:
                repository = USDTDataRepository(db)
                
                for data_point in historical_data:
                    try:
                        # 检查数据是否已存在
                        existing_data = await repository.get_data_at_time(
                            data_point['timestamp'],
                            tolerance_minutes=30
                        )
                        
                        if existing_data:
                            logger.debug(
                                "Historical data point already exists",
                                timestamp=data_point['timestamp']
                            )
                            continue
                        
                        # 处理数据
                        processed_data = await self._process_raw_data(data_point)
                        processed_data['is_anomaly'] = await self._detect_anomaly(processed_data)
                        
                        # 保存数据
                        usdt_data_create = USDTDataCreate(**processed_data)
                        saved_data = await repository.create(obj_in=usdt_data_create)
                        
                        collected_data.append(processed_data)
                        
                        # 添加小延迟避免过快插入
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(
                            "Failed to process historical data point",
                            timestamp=data_point.get('timestamp'),
                            error=str(e)
                        )
                        continue
            
            logger.info(
                "Historical data collection completed",
                total_points=len(historical_data),
                collected_points=len(collected_data)
            )
            
            return collected_data
            
        except Exception as e:
            logger.error("Historical data collection failed", error=str(e))
            raise AppException(
                error_code="HISTORICAL_DATA_COLLECTION_ERROR",
                message="历史数据收集失败",
                details={"error": str(e)}
            )
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取收集统计信息"""
        success_rate = 0
        if self.collection_stats['total_collections'] > 0:
            success_rate = (
                self.collection_stats['successful_collections'] / 
                self.collection_stats['total_collections']
            ) * 100
        
        return {
            'is_running': self.is_running,
            'collection_interval': self.collection_interval,
            'last_collection_time': self.last_collection_time,
            'total_collections': self.collection_stats['total_collections'],
            'successful_collections': self.collection_stats['successful_collections'],
            'failed_collections': self.collection_stats['failed_collections'],
            'success_rate': success_rate,
            'last_error': self.collection_stats['last_error']
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查外部API连接
            coingecko_healthy = await self.coingecko_client.health_check()
            
            # 检查数据库连接
            db_healthy = True
            try:
                async with get_db() as db:
                    await db.execute("SELECT 1")
            except Exception:
                db_healthy = False
            
            # 检查最近的数据收集
            recent_collection = (
                self.last_collection_time and 
                datetime.utcnow() - self.last_collection_time < timedelta(minutes=10)
            )
            
            overall_healthy = coingecko_healthy and db_healthy and (recent_collection or not self.is_running)
            
            return {
                'healthy': overall_healthy,
                'coingecko_api': coingecko_healthy,
                'database': db_healthy,
                'recent_collection': recent_collection,
                'is_running': self.is_running,
                'last_collection': self.last_collection_time
            }
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return {
                'healthy': False,
                'error': str(e)
            }


# 全局数据收集器实例
data_collector = DataCollector()
