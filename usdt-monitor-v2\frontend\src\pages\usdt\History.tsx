import React, { useState, useMemo } from 'react'
import {
  Card,
  Typography,
  Table,
  DatePicker,
  Select,
  Button,
  Space,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Input,
  message,
  Spin
} from 'antd'
import {
  SearchOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  Line<PERSON><PERSON>Outlined,
  WarningOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import { usdtApi } from '../../services/api/usdt'
import { PriceChart } from '../../components/charts/PriceChart'
import type { USDTData, USDTHistoryQuery } from '../../types/usdt'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

export default function History() {
  const [filters, setFilters] = useState<USDTHistoryQuery>({
    start_date: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    end_date: dayjs().format('YYYY-MM-DD'),
    interval: '1h'
  })
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [searchText, setSearchText] = useState('')

  // 获取历史数据
  const {
    data: historyData,
    isPending: isLoading,
    refetch
  } = useQuery({
    queryKey: ['usdt', 'history', filters, pagination.current, pagination.pageSize],
    queryFn: () => usdtApi.getHistoryData({
      ...filters,
      page: pagination.current,
      size: pagination.pageSize
    }),
    keepPreviousData: true
  })

  // 获取图表数据
  const {
    data: chartData,
    isPending: chartLoading
  } = useQuery({
    queryKey: ['usdt', 'chart', filters.start_date, filters.end_date, filters.interval],
    queryFn: () => usdtApi.getChartData(
      `${dayjs(filters.end_date).diff(dayjs(filters.start_date), 'day')}d`,
      filters.interval || '1h'
    ),
    enabled: !!filters.start_date && !!filters.end_date
  })

  const historyItems = historyData?.data?.items || []
  const total = historyData?.data?.total || 0

  // 更新分页信息
  React.useEffect(() => {
    setPagination(prev => ({ ...prev, total }))
  }, [total])

  // 表格列定义
  const columns: ColumnsType<USDTData> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => (
        <div>
          <div>{dayjs(timestamp).format('YYYY-MM-DD')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(timestamp).format('HH:mm:ss')}
          </Text>
        </div>
      ),
      sorter: true
    },
    {
      title: '价格 (USD)',
      dataIndex: 'current_price_usd',
      key: 'price',
      width: 150,
      render: (price: number, record: USDTData) => (
        <div className="text-right">
          <div className="font-mono text-sm">
            ${price.toFixed(6)}
          </div>
          {record.is_anomaly && (
            <Tag color="orange" size="small">
              <WarningOutlined className="mr-1" />
              异常
            </Tag>
          )}
        </div>
      ),
      sorter: true
    },
    {
      title: '24h变化',
      dataIndex: 'price_change_percentage_24h',
      key: 'change_24h',
      width: 120,
      render: (change: number) => {
        if (!change) return <Text type="secondary">-</Text>
        const color = change > 0 ? 'text-green-500' : change < 0 ? 'text-red-500' : 'text-gray-500'
        return (
          <div className={`text-right ${color}`}>
            {change > 0 ? '+' : ''}{change.toFixed(4)}%
          </div>
        )
      },
      sorter: true
    },
    {
      title: '交易量',
      dataIndex: 'volume_24h',
      key: 'volume',
      width: 120,
      render: (volume: number) => (
        <div className="text-right">
          {volume ? `$${(volume / 1e9).toFixed(2)}B` : '-'}
        </div>
      )
    },
    {
      title: '数据源',
      dataIndex: 'data_source',
      key: 'source',
      width: 100,
      render: (source: string) => (
        <Tag color="blue">{source}</Tag>
      ),
      filters: [
        { text: 'CoinGecko', value: 'coingecko' },
        { text: 'Binance', value: 'binance' },
        { text: 'Coinbase', value: 'coinbase' }
      ]
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence',
      width: 100,
      render: (score: number) => {
        if (!score) return <Text type="secondary">-</Text>
        const color = score > 0.8 ? 'green' : score > 0.6 ? 'orange' : 'red'
        return (
          <Tooltip title={`置信度: ${(score * 100).toFixed(1)}%`}>
            <Tag color={color}>{(score * 100).toFixed(0)}%</Tag>
          </Tooltip>
        )
      }
    }
  ]

  // 处理筛选变化
  const handleFiltersChange = (newFilters: Partial<USDTHistoryQuery>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setPagination(prev => ({ ...prev, current: 1 }))
  }

  // 处理表格变化
  const handleTableChange = (paginationConfig: any, filters: any, sorter: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }))
  }

  // 导出数据
  const handleExport = async () => {
    try {
      message.loading('正在准备导出数据...', 0)
      await usdtApi.exportData({
        start_date: filters.start_date,
        end_date: filters.end_date,
        format: 'csv',
        interval: filters.interval,
        fields: ['timestamp', 'current_price_usd', 'volume_24h', 'data_source']
      })
      message.destroy()
      message.success('导出请求已提交，请稍后查看导出状态')
    } catch (error) {
      message.destroy()
      message.error('导出失败，请重试')
    }
  }

  // 计算统计数据
  const statistics = useMemo(() => {
    if (!historyItems.length) return null

    const prices = historyItems.map(item => item.current_price_usd)
    const maxPrice = Math.max(...prices)
    const minPrice = Math.min(...prices)
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const anomalyCount = historyItems.filter(item => item.is_anomaly).length

    return {
      maxPrice,
      minPrice,
      avgPrice,
      anomalyCount,
      totalRecords: historyItems.length
    }
  }, [historyItems])

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            历史数据查看
          </Title>
          <Text type="secondary">
            查看和分析USDT历史价格数据
          </Text>
        </div>
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            disabled={!historyItems.length}
          >
            导出数据
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 筛选器 */}
      <Card title="数据筛选" size="small">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <div className="space-y-2">
              <Text strong>时间范围</Text>
              <RangePicker
                value={[dayjs(filters.start_date), dayjs(filters.end_date)]}
                onChange={(dates) => {
                  if (dates) {
                    handleFiltersChange({
                      start_date: dates[0]!.format('YYYY-MM-DD'),
                      end_date: dates[1]!.format('YYYY-MM-DD')
                    })
                  }
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <div className="space-y-2">
              <Text strong>时间间隔</Text>
              <Select
                value={filters.interval}
                onChange={(interval) => handleFiltersChange({ interval })}
                style={{ width: '100%' }}
              >
                <Option value="1m">1分钟</Option>
                <Option value="5m">5分钟</Option>
                <Option value="15m">15分钟</Option>
                <Option value="1h">1小时</Option>
                <Option value="4h">4小时</Option>
                <Option value="1d">1天</Option>
              </Select>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <div className="space-y-2">
              <Text strong>数据源</Text>
              <Select
                value={filters.source}
                onChange={(source) => handleFiltersChange({ source })}
                placeholder="选择数据源"
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="coingecko">CoinGecko</Option>
                <Option value="binance">Binance</Option>
                <Option value="coinbase">Coinbase</Option>
              </Select>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <div className="space-y-2">
              <Text strong>搜索</Text>
              <Input
                placeholder="搜索数据..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 统计概览 */}
      {statistics && (
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <Card size="small">
              <Statistic
                title="最高价"
                value={statistics.maxPrice}
                precision={6}
                prefix="$"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small">
              <Statistic
                title="最低价"
                value={statistics.minPrice}
                precision={6}
                prefix="$"
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small">
              <Statistic
                title="平均价"
                value={statistics.avgPrice}
                precision={6}
                prefix="$"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small">
              <Statistic
                title="异常数据"
                value={statistics.anomalyCount}
                suffix={`/ ${statistics.totalRecords}`}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 价格图表 */}
      <Card title="价格走势图" loading={chartLoading}>
        {chartData?.data && chartData.data.length > 0 ? (
          <PriceChart
            data={chartData.data}
            height={400}
            type="area"
            showVolume={false}
          />
        ) : (
          <div className="flex items-center justify-center h-96">
            <Text type="secondary">暂无图表数据</Text>
          </div>
        )}
      </Card>

      {/* 数据表格 */}
      <Card
        title={
          <div className="flex items-center justify-between">
            <span>历史数据列表</span>
            <Space>
              <Text type="secondary">
                共 {total} 条记录
              </Text>
              {selectedRowKeys.length > 0 && (
                <Text type="secondary">
                  已选择 {selectedRowKeys.length} 条
                </Text>
              )}
            </Space>
          </div>
        }
      >
        <Table
          columns={columns}
          dataSource={historyItems}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['20', '50', '100', '200']
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            selections: [
              Table.SELECTION_ALL,
              Table.SELECTION_INVERT,
              Table.SELECTION_NONE
            ]
          }}
          onChange={handleTableChange}
          scroll={{ x: 800 }}
          size="small"
        />
      </Card>
    </div>
  )
}
