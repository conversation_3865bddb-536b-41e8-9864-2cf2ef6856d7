"""
认证API测试
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.models.user import User as UserModel
from tests.conftest import assert_response_success, assert_response_error


class TestAuthAPI:
    """认证API测试类"""
    
    def test_login_success(self, client: TestClient, test_user: UserModel):
        """测试登录成功"""
        login_data = {
            "username": test_user.email,
            "password": "testpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        data = assert_response_success(response)
        
        assert "access_token" in data["data"]
        assert "refresh_token" in data["data"]
        assert data["data"]["token_type"] == "bearer"
        assert data["data"]["expires_in"] > 0
    
    def test_login_invalid_credentials(self, client: TestClient, test_user: UserModel):
        """测试登录失败 - 无效凭据"""
        login_data = {
            "username": test_user.email,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert_response_error(response, 401)
    
    def test_login_nonexistent_user(self, client: TestClient):
        """测试登录失败 - 用户不存在"""
        login_data = {
            "username": "<EMAIL>",
            "password": "password"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert_response_error(response, 401)
    
    def test_register_success(self, client: TestClient, sample_user_data: dict):
        """测试注册成功"""
        register_data = {
            **sample_user_data,
            "confirm_password": sample_user_data["password"]
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        data = assert_response_success(response)
        
        assert data["data"]["email"] == register_data["email"]
        assert data["data"]["username"] == register_data["username"]
        assert data["data"]["full_name"] == register_data["full_name"]
        assert "id" in data["data"]
    
    def test_register_duplicate_email(self, client: TestClient, test_user: UserModel):
        """测试注册失败 - 邮箱已存在"""
        register_data = {
            "email": test_user.email,
            "username": "newusername",
            "full_name": "New User",
            "password": "newpassword123",
            "confirm_password": "newpassword123"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert_response_error(response, 400)
    
    def test_register_password_mismatch(self, client: TestClient, sample_user_data: dict):
        """测试注册失败 - 密码不匹配"""
        register_data = {
            **sample_user_data,
            "confirm_password": "differentpassword"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert_response_error(response, 422)  # Validation error
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict, test_user: UserModel):
        """测试获取当前用户信息"""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        data = assert_response_success(response)
        
        assert data["data"]["email"] == test_user.email
        assert data["data"]["username"] == test_user.username
        assert data["data"]["full_name"] == test_user.full_name
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """测试获取当前用户信息 - 未授权"""
        response = client.get("/api/v1/auth/me")
        assert_response_error(response, 401)
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """测试获取当前用户信息 - 无效令牌"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        assert_response_error(response, 401)
    
    def test_logout(self, client: TestClient, auth_headers: dict):
        """测试登出"""
        response = client.post("/api/v1/auth/logout", headers=auth_headers)
        data = assert_response_success(response)
        
        assert "已成功登出" in data["data"]["message"]
    
    def test_change_password_success(self, client: TestClient, auth_headers: dict):
        """测试修改密码成功"""
        password_data = {
            "current_password": "testpassword",
            "new_password": "newtestpassword123",
            "confirm_password": "newtestpassword123"
        }
        
        response = client.post("/api/v1/auth/change-password", json=password_data, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "密码修改成功" in data["data"]["message"]
    
    def test_change_password_wrong_current(self, client: TestClient, auth_headers: dict):
        """测试修改密码失败 - 当前密码错误"""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newtestpassword123",
            "confirm_password": "newtestpassword123"
        }
        
        response = client.post("/api/v1/auth/change-password", json=password_data, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_change_password_mismatch(self, client: TestClient, auth_headers: dict):
        """测试修改密码失败 - 新密码不匹配"""
        password_data = {
            "current_password": "testpassword",
            "new_password": "newtestpassword123",
            "confirm_password": "differentpassword"
        }
        
        response = client.post("/api/v1/auth/change-password", json=password_data, headers=auth_headers)
        assert_response_error(response, 422)  # Validation error
    
    def test_forgot_password(self, client: TestClient, test_user: UserModel):
        """测试忘记密码"""
        email_data = {"email": test_user.email}
        
        response = client.post("/api/v1/auth/forgot-password", json=email_data)
        data = assert_response_success(response)
        
        assert "重置密码邮件已发送" in data["data"]["message"]
    
    def test_forgot_password_nonexistent_email(self, client: TestClient):
        """测试忘记密码 - 邮箱不存在"""
        email_data = {"email": "<EMAIL>"}
        
        response = client.post("/api/v1/auth/forgot-password", json=email_data)
        # 为了安全，即使邮箱不存在也返回成功
        data = assert_response_success(response)
        
        assert "重置密码邮件已发送" in data["data"]["message"]
    
    def test_refresh_token_success(self, client: TestClient, test_user: UserModel):
        """测试刷新令牌成功"""
        # 首先登录获取刷新令牌
        login_data = {
            "username": test_user.email,
            "password": "testpassword"
        }
        
        login_response = client.post("/api/v1/auth/login", data=login_data)
        login_result = assert_response_success(login_response)
        
        refresh_token = login_result["data"]["refresh_token"]
        
        # 使用刷新令牌获取新的访问令牌
        refresh_data = {"refresh_token": refresh_token}
        
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        data = assert_response_success(response)
        
        assert "access_token" in data["data"]
        assert data["data"]["token_type"] == "bearer"
    
    def test_refresh_token_invalid(self, client: TestClient):
        """测试刷新令牌失败 - 无效令牌"""
        refresh_data = {"refresh_token": "invalid_refresh_token"}
        
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        assert_response_error(response, 401)
    
    @pytest.mark.parametrize("missing_field", ["username", "password"])
    def test_login_missing_fields(self, client: TestClient, missing_field: str):
        """测试登录失败 - 缺少必需字段"""
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword"
        }
        
        del login_data[missing_field]
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert_response_error(response, 422)  # Validation error
    
    @pytest.mark.parametrize("missing_field", ["email", "username", "password"])
    def test_register_missing_fields(self, client: TestClient, missing_field: str, sample_user_data: dict):
        """测试注册失败 - 缺少必需字段"""
        register_data = {
            **sample_user_data,
            "confirm_password": sample_user_data["password"]
        }
        
        del register_data[missing_field]
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert_response_error(response, 422)  # Validation error
    
    def test_register_invalid_email(self, client: TestClient, sample_user_data: dict):
        """测试注册失败 - 无效邮箱格式"""
        register_data = {
            **sample_user_data,
            "email": "invalid_email",
            "confirm_password": sample_user_data["password"]
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert_response_error(response, 422)  # Validation error
    
    def test_register_weak_password(self, client: TestClient, sample_user_data: dict):
        """测试注册失败 - 弱密码"""
        register_data = {
            **sample_user_data,
            "password": "123",  # 太短的密码
            "confirm_password": "123"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert_response_error(response, 422)  # Validation error
