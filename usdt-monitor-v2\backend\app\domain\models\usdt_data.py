"""
USDT数据领域模型
"""
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import BigInteger, DateTime, Float, Index, Integer, Numeric, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class USDTData(Base):
    """USDT数据模型"""
    
    __tablename__ = "usdt_data"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 时间戳
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        index=True
    )
    unix_timestamp: Mapped[int] = mapped_column(
        BigInteger, 
        nullable=False, 
        index=True
    )
    
    # 基本信息
    name: Mapped[str] = mapped_column(String(50), default="Tether")
    symbol: Mapped[str] = mapped_column(String(10), default="USDT")
    
    # 价格信息 - 使用Decimal确保精度
    current_price_usd: Mapped[Decimal] = mapped_column(
        Numeric(precision=20, scale=10), 
        nullable=False,
        index=True
    )
    price_precision: Mapped[Optional[str]] = mapped_column(String(50))  # 高精度价格字符串
    
    # 市场数据
    market_cap_usd: Mapped[Optional[int]] = mapped_column(BigInteger)
    market_cap_rank: Mapped[Optional[int]] = mapped_column(Integer)
    total_volume_usd: Mapped[Optional[int]] = mapped_column(BigInteger)
    circulating_supply: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=2)
    )
    total_supply: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=2)
    )
    max_supply: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=2)
    )
    
    # 价格变化数据
    price_change_24h: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=10)
    )
    price_change_percentage_24h: Mapped[Optional[float]] = mapped_column(Float)
    price_change_7d: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=10)
    )
    price_change_percentage_7d: Mapped[Optional[float]] = mapped_column(Float)
    price_change_30d: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=10)
    )
    price_change_percentage_30d: Mapped[Optional[float]] = mapped_column(Float)
    
    # 市值变化数据
    market_cap_change_24h: Mapped[Optional[int]] = mapped_column(BigInteger)
    market_cap_change_percentage_24h: Mapped[Optional[float]] = mapped_column(Float)
    
    # 交易量数据
    volume_24h: Mapped[Optional[int]] = mapped_column(BigInteger)
    volume_change_24h: Mapped[Optional[float]] = mapped_column(Float)
    
    # 技术指标
    ath: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=10)
    )  # 历史最高价
    ath_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    atl: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=20, scale=10)
    )  # 历史最低价
    atl_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 数据源信息
    data_source: Mapped[str] = mapped_column(String(50), nullable=False)
    source_id: Mapped[Optional[str]] = mapped_column(String(100))  # 数据源中的ID
    last_updated: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 数据质量指标
    confidence_score: Mapped[Optional[float]] = mapped_column(Float)  # 数据可信度评分
    is_anomaly: Mapped[bool] = mapped_column(default=False)  # 是否为异常数据
    
    # 记录创建时间
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False
    )
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_usdt_data_timestamp_source', 'timestamp', 'data_source'),
        Index('idx_usdt_data_price_timestamp', 'current_price_usd', 'timestamp'),
        Index('idx_usdt_data_created_at', 'created_at'),
    )
    
    def __repr__(self) -> str:
        return f"<USDTData(id={self.id}, price={self.current_price_usd}, timestamp={self.timestamp})>"
    
    @property
    def price_float(self) -> float:
        """获取价格的浮点数表示"""
        return float(self.current_price_usd)
    
    @property
    def is_stable(self) -> bool:
        """判断价格是否稳定（接近1美元）"""
        return abs(float(self.current_price_usd) - 1.0) < 0.01
    
    def calculate_deviation_from_peg(self) -> float:
        """计算与锚定价格(1美元)的偏差百分比"""
        return (float(self.current_price_usd) - 1.0) * 100


class USDTDataAggregation(Base):
    """USDT数据聚合模型 - 用于存储预计算的统计数据"""
    
    __tablename__ = "usdt_data_aggregations"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 聚合时间信息
    period_start: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False,
        index=True
    )
    period_end: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False
    )
    interval_type: Mapped[str] = mapped_column(
        String(20), 
        nullable=False
    )  # 'minute', 'hour', 'day', 'week', 'month'
    
    # 价格统计
    open_price: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    close_price: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    high_price: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    low_price: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    avg_price: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    
    # 交易量统计
    total_volume: Mapped[Optional[int]] = mapped_column(BigInteger)
    avg_volume: Mapped[Optional[int]] = mapped_column(BigInteger)
    
    # 波动性指标
    volatility: Mapped[Optional[float]] = mapped_column(Float)
    price_range: Mapped[Decimal] = mapped_column(Numeric(precision=20, scale=10))
    
    # 数据点数量
    data_points_count: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # 创建时间
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False
    )
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_aggregation_period_interval', 'period_start', 'interval_type'),
        Index('idx_aggregation_created_at', 'created_at'),
    )
    
    def __repr__(self) -> str:
        return f"<USDTDataAggregation(period={self.period_start}, interval={self.interval_type})>"
