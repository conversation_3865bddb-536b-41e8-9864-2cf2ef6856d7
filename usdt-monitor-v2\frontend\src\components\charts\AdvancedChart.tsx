import React, { useMemo, useState } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  ComposedChart,
  Bar,
  ReferenceLine,
  Brush
} from 'recharts'
import { Card, Select, Space, Button, Typography, Switch, Slider } from 'antd'
import {
  LineChartOutlined,
  AreaChartOutlined,
  SettingOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import type { USDTChartData } from '../../types/usdt'

const { Text } = Typography
const { Option } = Select

// 技术指标类型
type Indicator = 'ma' | 'ema' | 'bollinger' | 'rsi' | 'macd'

interface AdvancedChartProps {
  data: USDTChartData[]
  height?: number
  showVolume?: boolean
  showIndicators?: boolean
  className?: string
}

// 计算移动平均线
const calculateMA = (data: USDTChartData[], period: number) => {
  return data.map((item, index) => {
    if (index < period - 1) return { ...item, [`ma${period}`]: null }
    
    const sum = data.slice(index - period + 1, index + 1)
      .reduce((acc, curr) => acc + curr.price, 0)
    
    return { ...item, [`ma${period}`]: sum / period }
  })
}

// 计算指数移动平均线
const calculateEMA = (data: USDTChartData[], period: number) => {
  const multiplier = 2 / (period + 1)
  let ema = data[0]?.price || 0
  
  return data.map((item, index) => {
    if (index === 0) {
      ema = item.price
    } else {
      ema = (item.price * multiplier) + (ema * (1 - multiplier))
    }
    return { ...item, [`ema${period}`]: ema }
  })
}

// 计算布林带
const calculateBollinger = (data: USDTChartData[], period: number = 20, stdDev: number = 2) => {
  return data.map((item, index) => {
    if (index < period - 1) {
      return { 
        ...item, 
        bollingerUpper: null, 
        bollingerMiddle: null, 
        bollingerLower: null 
      }
    }
    
    const slice = data.slice(index - period + 1, index + 1)
    const mean = slice.reduce((acc, curr) => acc + curr.price, 0) / period
    const variance = slice.reduce((acc, curr) => acc + Math.pow(curr.price - mean, 2), 0) / period
    const standardDeviation = Math.sqrt(variance)
    
    return {
      ...item,
      bollingerUpper: mean + (standardDeviation * stdDev),
      bollingerMiddle: mean,
      bollingerLower: mean - (standardDeviation * stdDev)
    }
  })
}

// 计算RSI
const calculateRSI = (data: USDTChartData[], period: number = 14) => {
  const changes = data.map((item, index) => {
    if (index === 0) return 0
    return item.price - data[index - 1].price
  })
  
  return data.map((item, index) => {
    if (index < period) return { ...item, rsi: null }
    
    const recentChanges = changes.slice(index - period + 1, index + 1)
    const gains = recentChanges.filter(change => change > 0)
    const losses = recentChanges.filter(change => change < 0).map(loss => Math.abs(loss))
    
    const avgGain = gains.length > 0 ? gains.reduce((a, b) => a + b, 0) / period : 0
    const avgLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / period : 0
    
    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss
    const rsi = 100 - (100 / (1 + rs))
    
    return { ...item, rsi }
  })
}

export const AdvancedChart: React.FC<AdvancedChartProps> = ({
  data,
  height = 400,
  showVolume = false,
  showIndicators = false,
  className
}) => {
  const [chartType, setChartType] = useState<'line' | 'area' | 'candlestick'>('line')
  const [selectedIndicators, setSelectedIndicators] = useState<Indicator[]>(['ma'])
  const [maPeriods, setMaPeriods] = useState([5, 10, 20])
  const [showGrid, setShowGrid] = useState(true)
  const [showBrush, setShowBrush] = useState(false)
  const [priceRange, setPriceRange] = useState<[number, number] | null>(null)

  // 处理数据并添加技术指标
  const processedData = useMemo(() => {
    if (!data.length) return []
    
    let result = data.map(item => ({
      ...item,
      timestamp: dayjs(item.timestamp).valueOf(),
      formattedTime: dayjs(item.timestamp).format('HH:mm'),
      formattedDate: dayjs(item.timestamp).format('MM-DD HH:mm'),
      price: Number(item.price),
      volume: item.volume ? Number(item.volume) : 0,
    }))

    // 添加移动平均线
    if (selectedIndicators.includes('ma')) {
      maPeriods.forEach(period => {
        result = calculateMA(result, period)
      })
    }

    // 添加指数移动平均线
    if (selectedIndicators.includes('ema')) {
      result = calculateEMA(result, 12)
      result = calculateEMA(result, 26)
    }

    // 添加布林带
    if (selectedIndicators.includes('bollinger')) {
      result = calculateBollinger(result)
    }

    // 添加RSI
    if (selectedIndicators.includes('rsi')) {
      result = calculateRSI(result)
    }

    return result
  }, [data, selectedIndicators, maPeriods])

  // 计算价格范围
  const priceExtent = useMemo(() => {
    if (!processedData.length) return [0, 1]
    const prices = processedData.map(d => d.price)
    const min = Math.min(...prices)
    const max = Math.max(...prices)
    const padding = (max - min) * 0.05
    return [min - padding, max + padding]
  }, [processedData])

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {dayjs(label).format('YYYY-MM-DD HH:mm:ss')}
          </p>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">价格:</span>
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                ${data.price.toFixed(6)}
              </span>
            </div>
            {showVolume && data.volume > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">交易量:</span>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  ${(data.volume / 1e9).toFixed(2)}B
                </span>
              </div>
            )}
            {selectedIndicators.includes('rsi') && data.rsi && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">RSI:</span>
                <span className="text-sm font-medium">
                  {data.rsi.toFixed(2)}
                </span>
              </div>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  // 格式化X轴标签
  const formatXAxisLabel = (tickItem: number) => {
    return dayjs(tickItem).format('HH:mm')
  }

  // 格式化Y轴标签
  const formatYAxisLabel = (value: number) => {
    return `$${value.toFixed(4)}`
  }

  // 渲染主图表
  const renderMainChart = () => {
    const commonProps = {
      data: processedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    }

    const chartContent = (
      <>
        {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
        <XAxis 
          dataKey="timestamp"
          type="number"
          scale="time"
          domain={['dataMin', 'dataMax']}
          tickFormatter={formatXAxisLabel}
          stroke="#666"
        />
        <YAxis 
          domain={priceRange || [priceExtent[0], priceExtent[1]]}
          tickFormatter={formatYAxisLabel}
          stroke="#666"
        />
        <Tooltip content={<CustomTooltip />} />
        
        {/* 价格线/面积 */}
        {chartType === 'area' ? (
          <Area
            type="monotone"
            dataKey="price"
            stroke="#1890ff"
            strokeWidth={2}
            fill="url(#priceGradient)"
          />
        ) : (
          <Line
            type="monotone"
            dataKey="price"
            stroke="#1890ff"
            strokeWidth={2}
            dot={false}
            activeDot={{ r: 4, stroke: '#1890ff', strokeWidth: 2 }}
          />
        )}

        {/* 移动平均线 */}
        {selectedIndicators.includes('ma') && maPeriods.map((period, index) => (
          <Line
            key={`ma${period}`}
            type="monotone"
            dataKey={`ma${period}`}
            stroke={['#ff7300', '#00ff00', '#ff00ff'][index]}
            strokeWidth={1}
            dot={false}
            strokeDasharray="5 5"
          />
        ))}

        {/* 指数移动平均线 */}
        {selectedIndicators.includes('ema') && (
          <>
            <Line
              type="monotone"
              dataKey="ema12"
              stroke="#ff7300"
              strokeWidth={1}
              dot={false}
              strokeDasharray="3 3"
            />
            <Line
              type="monotone"
              dataKey="ema26"
              stroke="#00ff00"
              strokeWidth={1}
              dot={false}
              strokeDasharray="3 3"
            />
          </>
        )}

        {/* 布林带 */}
        {selectedIndicators.includes('bollinger') && (
          <>
            <Line
              type="monotone"
              dataKey="bollingerUpper"
              stroke="#ff4d4f"
              strokeWidth={1}
              dot={false}
              strokeDasharray="2 2"
            />
            <Line
              type="monotone"
              dataKey="bollingerMiddle"
              stroke="#faad14"
              strokeWidth={1}
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="bollingerLower"
              stroke="#52c41a"
              strokeWidth={1}
              dot={false}
              strokeDasharray="2 2"
            />
          </>
        )}

        {/* 参考线 */}
        <ReferenceLine y={1.0} stroke="#ff4d4f" strokeDasharray="5 5" />

        {/* 时间刷选器 */}
        {showBrush && (
          <Brush
            dataKey="timestamp"
            height={30}
            stroke="#1890ff"
            tickFormatter={formatXAxisLabel}
          />
        )}
      </>
    )

    if (chartType === 'area') {
      return (
        <AreaChart {...commonProps}>
          <defs>
            <linearGradient id="priceGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#1890ff" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#1890ff" stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          {chartContent}
        </AreaChart>
      )
    }

    if (showVolume) {
      return (
        <ComposedChart {...commonProps}>
          {chartContent}
          <Bar
            yAxisId="volume"
            dataKey="volume"
            fill="#52c41a"
            opacity={0.3}
          />
          <YAxis 
            yAxisId="volume"
            orientation="right"
            tickFormatter={(value) => `${(value / 1e9).toFixed(1)}B`}
            stroke="#666"
          />
        </ComposedChart>
      )
    }

    return <LineChart {...commonProps}>{chartContent}</LineChart>
  }

  // 渲染RSI指标
  const renderRSIChart = () => {
    if (!selectedIndicators.includes('rsi')) return null

    return (
      <div className="mt-4">
        <Text strong className="block mb-2">RSI 指标</Text>
        <ResponsiveContainer width="100%" height={120}>
          <LineChart data={processedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={formatXAxisLabel}
              stroke="#666"
            />
            <YAxis domain={[0, 100]} stroke="#666" />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="rsi"
              stroke="#722ed1"
              strokeWidth={2}
              dot={false}
            />
            <ReferenceLine y={70} stroke="#ff4d4f" strokeDasharray="3 3" />
            <ReferenceLine y={30} stroke="#52c41a" strokeDasharray="3 3" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    )
  }

  if (!processedData.length) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <Text type="secondary">暂无图表数据</Text>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* 图表控制栏 */}
      <div className="flex items-center justify-between mb-4">
        <Space>
          <Select
            value={chartType}
            onChange={setChartType}
            style={{ width: 100 }}
          >
            <Option value="line">
              <Space>
                <LineChartOutlined />
                线图
              </Space>
            </Option>
            <Option value="area">
              <Space>
                <AreaChartOutlined />
                面积图
              </Space>
            </Option>
          </Select>

          <Select
            mode="multiple"
            value={selectedIndicators}
            onChange={setSelectedIndicators}
            placeholder="技术指标"
            style={{ minWidth: 150 }}
          >
            <Option value="ma">移动平均线</Option>
            <Option value="ema">指数移动平均</Option>
            <Option value="bollinger">布林带</Option>
            <Option value="rsi">RSI</Option>
          </Select>

          {selectedIndicators.includes('ma') && (
            <Select
              mode="multiple"
              value={maPeriods}
              onChange={setMaPeriods}
              placeholder="MA周期"
              style={{ width: 120 }}
            >
              <Option value={5}>MA5</Option>
              <Option value={10}>MA10</Option>
              <Option value={20}>MA20</Option>
              <Option value={50}>MA50</Option>
            </Select>
          )}
        </Space>

        <Space>
          <Switch
            checked={showGrid}
            onChange={setShowGrid}
            checkedChildren="网格"
            unCheckedChildren="网格"
          />
          <Switch
            checked={showBrush}
            onChange={setShowBrush}
            checkedChildren="刷选"
            unCheckedChildren="刷选"
          />
          <Button icon={<SettingOutlined />} size="small">
            设置
          </Button>
          <Button icon={<FullscreenOutlined />} size="small">
            全屏
          </Button>
        </Space>
      </div>

      {/* 主图表 */}
      <ResponsiveContainer width="100%" height={height}>
        {renderMainChart()}
      </ResponsiveContainer>

      {/* RSI指标图 */}
      {renderRSIChart()}

      {/* 价格范围滑块 */}
      {priceRange && (
        <div className="mt-4">
          <Text strong className="block mb-2">价格范围</Text>
          <Slider
            range
            min={priceExtent[0]}
            max={priceExtent[1]}
            step={0.000001}
            value={priceRange}
            onChange={setPriceRange}
            tipFormatter={(value) => `$${value?.toFixed(6)}`}
          />
        </div>
      )}
    </div>
  )
}
