// 用户类型
export interface User {
  id: string
  email: string
  username: string
  full_name: string
  avatar_url?: string
  phone?: string
  bio?: string
  timezone?: string
  is_active: boolean
  is_verified: boolean
  is_superuser: boolean
  permissions?: string[]
  last_login?: string
  created_at: string
  updated_at?: string
}

// 登录请求
export interface LoginRequest {
  username: string // 可以是邮箱或用户名
  password: string
  remember_me?: boolean
}

// 登录响应
export interface LoginResponse {
  user: User
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

// 注册请求
export interface RegisterRequest {
  email: string
  username: string
  full_name: string
  password: string
  confirm_password: string
  terms_accepted?: boolean
}

// 修改密码请求
export interface ChangePasswordRequest {
  current_password: string
  new_password: string
  confirm_password: string
}

// 忘记密码请求
export interface ForgotPasswordRequest {
  email: string
}

// 重置密码请求
export interface ResetPasswordRequest {
  token: string
  new_password: string
  confirm_password: string
}

// 用户资料更新
export interface UserProfileUpdate {
  full_name?: string
  phone?: string
  avatar_url?: string
  bio?: string
  timezone?: string
}

// 用户设置
export interface UserSettings {
  email_notifications: boolean
  push_notifications: boolean
  alert_notifications: boolean
  language: string
  timezone: string
  theme: 'light' | 'dark' | 'auto'
}

// 权限类型
export type Permission = 
  | 'user:read'
  | 'user:write'
  | 'user:delete'
  | 'admin:read'
  | 'admin:write'
  | 'system:read'
  | 'system:write'
  | 'usdt:read'
  | 'usdt:write'
  | 'usdt:export'
  | 'alert:read'
  | 'alert:write'

// 角色类型
export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  is_default: boolean
  created_at: string
}

// 用户活动日志
export interface UserActivity {
  id: string
  user_id: string
  action: string
  resource?: string
  details?: string
  ip_address?: string
  user_agent?: string
  created_at: string
}
