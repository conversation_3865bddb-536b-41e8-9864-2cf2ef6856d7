"""
基础仓储类
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from uuid import UUID

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """基础仓储类"""
    
    def __init__(self, model: type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db
    
    async def get(self, id: UUID) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_multi(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """获取多个对象"""
        query = select(self.model)
        
        # 应用过滤条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        conditions.append(getattr(self.model, key).in_(value))
                    else:
                        conditions.append(getattr(self.model, key) == value)
            if conditions:
                query = query.where(and_(*conditions))
        
        # 应用排序
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                order_column = order_column.desc()
            query = query.order_by(order_column)
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取对象总数"""
        query = select(func.count(self.model.id))
        
        # 应用过滤条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        conditions.append(getattr(self.model, key).in_(value))
                    else:
                        conditions.append(getattr(self.model, key) == value)
            if conditions:
                query = query.where(and_(*conditions))
        
        result = await self.db.execute(query)
        return result.scalar()
    
    async def create(self, *, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def update(
        self,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """更新对象"""
        obj_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in
        
        for field, value in obj_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def remove(self, *, id: UUID) -> Optional[ModelType]:
        """删除对象"""
        obj = await self.get(id)
        if obj:
            await self.db.delete(obj)
            await self.db.commit()
        return obj
    
    async def exists(self, id: UUID) -> bool:
        """检查对象是否存在"""
        result = await self.db.execute(
            select(func.count(self.model.id)).where(self.model.id == id)
        )
        return result.scalar() > 0
    
    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """根据字段值获取对象"""
        if not hasattr(self.model, field):
            return None
        
        result = await self.db.execute(
            select(self.model).where(getattr(self.model, field) == value)
        )
        return result.scalar_one_or_none()
    
    async def get_multi_by_field(
        self,
        field: str,
        value: Any,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """根据字段值获取多个对象"""
        if not hasattr(self.model, field):
            return []
        
        result = await self.db.execute(
            select(self.model)
            .where(getattr(self.model, field) == value)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def search(
        self,
        query: str,
        fields: List[str],
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """搜索对象"""
        if not query or not fields:
            return []
        
        conditions = []
        for field in fields:
            if hasattr(self.model, field):
                field_attr = getattr(self.model, field)
                if hasattr(field_attr.type, 'python_type') and field_attr.type.python_type == str:
                    conditions.append(field_attr.ilike(f"%{query}%"))
        
        if not conditions:
            return []
        
        result = await self.db.execute(
            select(self.model)
            .where(or_(*conditions))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[ModelType]:
        """批量创建对象"""
        db_objs = []
        for obj_in in objs_in:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
            db_obj = self.model(**obj_data)
            db_objs.append(db_obj)
        
        self.db.add_all(db_objs)
        await self.db.commit()
        
        for db_obj in db_objs:
            await self.db.refresh(db_obj)
        
        return db_objs
    
    async def bulk_update(
        self,
        updates: List[Dict[str, Any]]
    ) -> int:
        """批量更新对象"""
        if not updates:
            return 0
        
        # 这里可以实现更高效的批量更新逻辑
        # 目前使用简单的逐个更新方式
        updated_count = 0
        for update_data in updates:
            if 'id' in update_data:
                obj_id = update_data.pop('id')
                obj = await self.get(obj_id)
                if obj:
                    await self.update(db_obj=obj, obj_in=update_data)
                    updated_count += 1
        
        return updated_count
    
    async def bulk_delete(self, ids: List[UUID]) -> int:
        """批量删除对象"""
        if not ids:
            return 0
        
        result = await self.db.execute(
            select(func.count(self.model.id)).where(self.model.id.in_(ids))
        )
        count = result.scalar()
        
        if count > 0:
            await self.db.execute(
                self.model.__table__.delete().where(self.model.id.in_(ids))
            )
            await self.db.commit()
        
        return count


class ReadOnlyRepository(Generic[ModelType], ABC):
    """只读仓储基类"""
    
    def __init__(self, model: type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db
    
    async def get(self, id: UUID) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_multi(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[ModelType]:
        """获取多个对象"""
        query = select(self.model)
        
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    conditions.append(getattr(self.model, key) == value)
            if conditions:
                query = query.where(and_(*conditions))
        
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取对象总数"""
        query = select(func.count(self.model.id))
        
        if filters:
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    conditions.append(getattr(self.model, key) == value)
            if conditions:
                query = query.where(and_(*conditions))
        
        result = await self.db.execute(query)
        return result.scalar()
