# USDT Monitor Platform V2 - Environment Variables
# Copy this file to .env and update the values for your environment

# =============================================================================
# 应用配置
# =============================================================================
PROJECT_NAME="USDT Monitor Platform V2"
VERSION="2.0.0"
ENVIRONMENT=development
DEBUG=true

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=usdt_monitor
POSTGRES_PASSWORD=usdt_monitor_password
POSTGRES_DB=usdt_monitor

# 数据库URL (自动生成，通常不需要修改)
DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_SERVER}:${POSTGRES_PORT}/${POSTGRES_DB}

# =============================================================================
# Redis配置
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=usdt_monitor_redis_password
REDIS_DB=0

# Redis URL (自动生成，通常不需要修改)
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 (生产环境必须更改为强密钥)
SECRET_KEY=your-super-secret-key-change-in-production-environment

# JWT令牌过期时间
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码加密算法
ALGORITHM=HS256

# =============================================================================
# 管理员账户配置
# =============================================================================
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123456
FIRST_SUPERUSER_USERNAME=admin
FIRST_SUPERUSER_FULL_NAME="System Administrator"

# =============================================================================
# 外部API配置
# =============================================================================
# CoinGecko API
COINGECKO_API_URL=https://api.coingecko.com/api/v3
COINGECKO_API_KEY=

# 其他数据源API (可选)
BINANCE_API_URL=https://api.binance.com/api/v3
COINBASE_API_URL=https://api.coinbase.com/v2

# =============================================================================
# 监控配置
# =============================================================================
# 数据收集间隔 (秒)
MONITORING_INTERVAL=30

# 价格异常告警阈值 (偏离1美元的百分比)
ALERT_THRESHOLD=0.01

# 数据保留天数
DATA_RETENTION_DAYS=90

# 异常检测敏感度 (1-10, 10最敏感)
ANOMALY_DETECTION_SENSITIVITY=5

# =============================================================================
# 性能配置
# =============================================================================
# 缓存TTL (秒)
CACHE_TTL=300

# API速率限制 (每分钟请求数)
RATE_LIMIT_PER_MINUTE=60

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# =============================================================================
# 文件存储配置
# =============================================================================
# 上传文件目录
UPLOAD_DIR=./uploads

# 导出文件目录
EXPORT_DIR=./exports

# 日志文件目录
LOG_DIR=./logs

# 最大文件大小 (MB)
MAX_FILE_SIZE=10

# =============================================================================
# 邮件配置 (可选)
# =============================================================================
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 邮件发送者信息
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="USDT Monitor Platform"

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# Sentry错误追踪 (可选)
SENTRY_DSN=

# =============================================================================
# 监控和指标配置
# =============================================================================
# Prometheus
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8001

# 健康检查
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# 前端配置
# =============================================================================
# API URL
REACT_APP_API_URL=http://localhost:8000/api/v1

# WebSocket URL
REACT_APP_WS_URL=ws://localhost:8000/ws

# 应用信息
REACT_APP_VERSION=2.0.0
REACT_APP_ENVIRONMENT=development

# 功能开关
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_NOTIFICATIONS=true

# =============================================================================
# Docker配置
# =============================================================================
# 构建目标 (development/production)
BUILD_TARGET=development

# 端口映射
BACKEND_PORT=8000
FRONTEND_PORT=3000
POSTGRES_PORT=5432
REDIS_PORT=6379
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
PROMETHEUS_WEB_PORT=9090
GRAFANA_PORT=3001

# =============================================================================
# SSL/TLS配置 (生产环境)
# =============================================================================
# SSL证书路径
SSL_CERT_PATH=./nginx/ssl/cert.pem
SSL_KEY_PATH=./nginx/ssl/key.pem

# 强制HTTPS
FORCE_HTTPS=false

# =============================================================================
# 备份配置
# =============================================================================
# 自动备份
AUTO_BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_DIR=./backups

# =============================================================================
# 开发配置
# =============================================================================
# 热重载
HOT_RELOAD=true

# 调试模式
ENABLE_DEBUG_TOOLBAR=true

# 测试数据库
TEST_DATABASE_URL=sqlite+aiosqlite:///:memory:

# =============================================================================
# 生产环境特定配置
# =============================================================================
# 工作进程数
WORKERS=4

# 最大请求数
MAX_REQUESTS=1000

# 请求超时 (秒)
TIMEOUT=30

# 内存限制 (MB)
MEMORY_LIMIT=512

# =============================================================================
# 第三方服务配置
# =============================================================================
# Google Analytics (可选)
GA_TRACKING_ID=

# Slack通知 (可选)
SLACK_WEBHOOK_URL=

# Discord通知 (可选)
DISCORD_WEBHOOK_URL=

# =============================================================================
# 安全增强配置
# =============================================================================
# CORS配置
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# 信任的代理
TRUSTED_HOSTS=["localhost", "127.0.0.1"]

# 会话安全
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=lax

# =============================================================================
# 实验性功能
# =============================================================================
# 启用实验性功能
ENABLE_EXPERIMENTAL_FEATURES=false

# 机器学习预测
ENABLE_ML_PREDICTIONS=false

# 高级分析
ENABLE_ADVANCED_ANALYTICS=false
