"""
USDT数据监控API端点
"""
from datetime import datetime, timed<PERSON>ta
from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from fastapi.responses import StreamingResponse

from app.api.deps import get_current_active_user
from app.domain.schemas.common import ApiResponse, PaginatedResponse, PaginationParams
from app.domain.schemas.usdt import (
    USDTData, USDTDataCreate, USDTStats, USDTHistoryQuery,
    USDTAlert, USDTAlertCreate, USDTExportRequest, USDTDataResponse
)
from app.domain.schemas.user import User
from app.application.services.usdt_service import USDTService
from app.application.services.export_service import ExportService

router = APIRouter()


@router.get("/current", response_model=ApiResponse[USDTData])
async def get_current_usdt_data(
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    获取当前USDT数据
    """
    try:
        current_data = await usdt_service.get_current_data()
        if not current_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="暂无USDT数据"
            )
        
        return ApiResponse(
            data=current_data,
            message="获取当前USDT数据成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取USDT数据失败"
        )


@router.get("/history", response_model=ApiResponse[PaginatedResponse[USDTData]])
async def get_usdt_history(
    pagination: PaginationParams = Depends(),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    interval: str = Query("1h", description="时间间隔 (1m, 5m, 15m, 1h, 4h, 1d)"),
    source: Optional[str] = Query(None, description="数据源"),
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    获取USDT历史数据
    """
    try:
        # 设置默认时间范围（最近24小时）
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(hours=24)
        
        # 验证时间范围
        if start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="开始时间必须早于结束时间"
            )
        
        # 限制查询范围（最多90天）
        max_range = timedelta(days=90)
        if end_date - start_date > max_range:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="查询时间范围不能超过90天"
            )
        
        history_data, total = await usdt_service.get_history_data(
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            source=source,
            offset=pagination.offset,
            limit=pagination.page_size
        )
        
        paginated_response = PaginatedResponse.create(
            items=history_data,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        return ApiResponse(
            data=paginated_response,
            message="获取历史数据成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取历史数据失败"
        )


@router.get("/stats", response_model=ApiResponse[USDTStats])
async def get_usdt_stats(
    period: str = Query("24h", description="统计周期 (1h, 24h, 7d, 30d)"),
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    获取USDT统计数据
    """
    try:
        stats = await usdt_service.get_stats(period)
        
        return ApiResponse(
            data=stats,
            message="获取统计数据成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计数据失败"
        )


@router.get("/price-chart", response_model=ApiResponse[List[USDTDataResponse]])
async def get_price_chart_data(
    period: str = Query("24h", description="时间周期"),
    interval: str = Query("1h", description="数据间隔"),
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    获取价格图表数据
    """
    try:
        chart_data = await usdt_service.get_chart_data(period, interval)
        
        return ApiResponse(
            data=chart_data,
            message="获取图表数据成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取图表数据失败"
        )


@router.post("/alerts", response_model=ApiResponse[USDTAlert])
async def create_price_alert(
    alert_data: USDTAlertCreate,
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    创建价格告警
    """
    try:
        alert = await usdt_service.create_alert(
            user_id=current_user.id,
            alert_data=alert_data
        )
        
        return ApiResponse(
            data=alert,
            message="价格告警创建成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建价格告警失败"
        )


@router.get("/alerts", response_model=ApiResponse[PaginatedResponse[USDTAlert]])
async def get_user_alerts(
    pagination: PaginationParams = Depends(),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    获取用户的价格告警列表
    """
    try:
        alerts, total = await usdt_service.get_user_alerts(
            user_id=current_user.id,
            is_active=is_active,
            offset=pagination.offset,
            limit=pagination.page_size
        )
        
        paginated_response = PaginatedResponse.create(
            items=alerts,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        return ApiResponse(
            data=paginated_response,
            message="获取告警列表成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取告警列表失败"
        )


@router.delete("/alerts/{alert_id}", response_model=ApiResponse[dict])
async def delete_price_alert(
    alert_id: UUID,
    current_user: User = Depends(get_current_active_user),
    usdt_service: USDTService = Depends()
) -> Any:
    """
    删除价格告警
    """
    try:
        success = await usdt_service.delete_alert(alert_id, current_user.id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="告警不存在或无权限删除"
            )
        
        return ApiResponse(
            data={"message": "告警删除成功"},
            message="告警删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除告警失败"
        )


@router.post("/export", response_model=ApiResponse[dict])
async def export_usdt_data(
    export_request: USDTExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    export_service: ExportService = Depends()
) -> Any:
    """
    导出USDT数据
    """
    try:
        # 验证导出请求
        if export_request.start_date >= export_request.end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="开始时间必须早于结束时间"
            )
        
        # 限制导出范围（最多30天）
        max_range = timedelta(days=30)
        if export_request.end_date - export_request.start_date > max_range:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="导出时间范围不能超过30天"
            )
        
        # 创建导出任务
        export_id = await export_service.create_export_task(
            user_id=current_user.id,
            export_request=export_request
        )
        
        # 添加后台任务
        background_tasks.add_task(
            export_service.process_export_task,
            export_id
        )
        
        return ApiResponse(
            data={
                "export_id": export_id,
                "message": "导出任务已创建，请稍后查看导出状态"
            },
            message="导出任务创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建导出任务失败"
        )


@router.get("/export/{export_id}/status", response_model=ApiResponse[dict])
async def get_export_status(
    export_id: UUID,
    current_user: User = Depends(get_current_active_user),
    export_service: ExportService = Depends()
) -> Any:
    """
    获取导出任务状态
    """
    try:
        status_info = await export_service.get_export_status(export_id, current_user.id)
        if not status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="导出任务不存在"
            )
        
        return ApiResponse(
            data=status_info,
            message="获取导出状态成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取导出状态失败"
        )


@router.get("/export/{export_id}/download")
async def download_export_file(
    export_id: UUID,
    current_user: User = Depends(get_current_active_user),
    export_service: ExportService = Depends()
) -> StreamingResponse:
    """
    下载导出文件
    """
    try:
        file_stream, filename, media_type = await export_service.download_export_file(
            export_id, current_user.id
        )
        
        return StreamingResponse(
            file_stream,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="导出文件不存在或已过期"
        )
    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限下载此文件"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载文件失败"
        )
