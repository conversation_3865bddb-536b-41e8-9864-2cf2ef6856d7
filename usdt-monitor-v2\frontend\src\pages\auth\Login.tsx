import React, { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { Form, Input, Button, Checkbox, Alert, Space, Typography } from 'antd'
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { useAuthStore } from '../../stores/authStore'
import type { LoginRequest } from '../../types/auth'

const { Text } = Typography

interface LocationState {
  from?: {
    pathname: string
  }
}

export default function Login() {
  const navigate = useNavigate()
  const location = useLocation()
  const { login } = useAuthStore()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取重定向路径
  const from = (location.state as LocationState)?.from?.pathname || '/'

  const handleSubmit = async (values: LoginRequest & { remember?: boolean }) => {
    setLoading(true)
    setError(null)

    try {
      await login({
        username: values.username,
        password: values.password,
        remember_me: values.remember,
      })
      
      // 登录成功，重定向到目标页面
      navigate(from, { replace: true })
    } catch (err: any) {
      setError(err.message || '登录失败，请检查用户名和密码')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          登录账户
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          欢迎回来，请登录您的账户
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* 登录表单 */}
      <Form
        name="login"
        size="large"
        onFinish={handleSubmit}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="username"
          label="邮箱或用户名"
          rules={[
            { required: true, message: '请输入邮箱或用户名' },
            { min: 3, message: '用户名至少3个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入邮箱或用户名"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            autoComplete="current-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item>
          <div className="flex items-center justify-between">
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox>记住我</Checkbox>
            </Form.Item>
            <Link
              to="/auth/forgot-password"
              className="text-primary hover:text-primary-dark"
            >
              忘记密码？
            </Link>
          </div>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            size="large"
          >
            {loading ? '登录中...' : '登录'}
          </Button>
        </Form.Item>
      </Form>

      {/* 注册链接 */}
      <div className="text-center">
        <Space>
          <Text type="secondary">还没有账户？</Text>
          <Link
            to="/auth/register"
            className="text-primary hover:text-primary-dark font-medium"
          >
            立即注册
          </Link>
        </Space>
      </div>

      {/* 演示账户 */}
      <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <Text type="secondary" className="text-sm">
          <strong>演示账户：</strong>
        </Text>
        <div className="mt-2 space-y-1 text-sm">
          <div>管理员：<EMAIL> / admin123456</div>
          <div>普通用户：<EMAIL> / user123456</div>
        </div>
      </div>
    </div>
  )
}
