"""
USDT数据仓储实现
"""
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import List, Optional, Tuple, Dict, Any

from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.models.usdt_data import USDTData as USDTDataModel, USDTDataAggregation
from app.domain.schemas.usdt import USDTDataCreate, USDTData
from app.infrastructure.repositories.base import BaseRepository


class USDTDataRepository(BaseRepository[USDTDataModel, USDTDataCreate, dict]):
    """USDT数据仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(USDTDataModel, db)
    
    async def get_latest_data(self, source: Optional[str] = None) -> Optional[USDTDataModel]:
        """获取最新的USDT数据"""
        query = select(USDTDataModel).order_by(desc(USDTDataModel.timestamp))
        
        if source:
            query = query.where(USDTDataModel.data_source == source)
        
        result = await self.db.execute(query.limit(1))
        return result.scalar_one_or_none()
    
    async def get_data_by_time_range(
        self,
        start_time: datetime,
        end_time: datetime,
        *,
        source: Optional[str] = None,
        interval_minutes: Optional[int] = None,
        skip: int = 0,
        limit: int = 1000
    ) -> Tuple[List[USDTDataModel], int]:
        """根据时间范围获取数据"""
        conditions = [
            USDTDataModel.timestamp >= start_time,
            USDTDataModel.timestamp <= end_time
        ]
        
        if source:
            conditions.append(USDTDataModel.data_source == source)
        
        # 如果指定了间隔，进行数据抽样
        base_query = select(USDTDataModel).where(and_(*conditions))
        
        if interval_minutes:
            # 简单的时间间隔过滤，实际应用中可能需要更复杂的聚合逻辑
            base_query = base_query.where(
                func.extract('minute', USDTDataModel.timestamp) % interval_minutes == 0
            )
        
        # 获取总数
        count_query = select(func.count(USDTDataModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = base_query.order_by(USDTDataModel.timestamp).offset(skip).limit(limit)
        result = await self.db.execute(query)
        data = result.scalars().all()
        
        return data, total
    
    async def get_price_statistics(
        self,
        start_time: datetime,
        end_time: datetime,
        source: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取价格统计信息"""
        conditions = [
            USDTDataModel.timestamp >= start_time,
            USDTDataModel.timestamp <= end_time
        ]
        
        if source:
            conditions.append(USDTDataModel.data_source == source)
        
        # 执行统计查询
        result = await self.db.execute(
            select(
                func.count(USDTDataModel.id).label('count'),
                func.avg(USDTDataModel.current_price_usd).label('avg_price'),
                func.min(USDTDataModel.current_price_usd).label('min_price'),
                func.max(USDTDataModel.current_price_usd).label('max_price'),
                func.stddev(USDTDataModel.current_price_usd).label('price_stddev'),
                func.avg(USDTDataModel.volume_24h).label('avg_volume'),
                func.sum(USDTDataModel.volume_24h).label('total_volume')
            ).where(and_(*conditions))
        )
        
        stats = result.first()
        
        if not stats or stats.count == 0:
            return {
                'count': 0,
                'avg_price': None,
                'min_price': None,
                'max_price': None,
                'price_range': None,
                'volatility': None,
                'avg_volume': None,
                'total_volume': None
            }
        
        price_range = float(stats.max_price - stats.min_price) if stats.max_price and stats.min_price else 0
        volatility = float(stats.price_stddev / stats.avg_price) if stats.price_stddev and stats.avg_price else 0
        
        return {
            'count': stats.count,
            'avg_price': float(stats.avg_price) if stats.avg_price else None,
            'min_price': float(stats.min_price) if stats.min_price else None,
            'max_price': float(stats.max_price) if stats.max_price else None,
            'price_range': price_range,
            'volatility': volatility,
            'avg_volume': int(stats.avg_volume) if stats.avg_volume else None,
            'total_volume': int(stats.total_volume) if stats.total_volume else None
        }
    
    async def get_price_changes(
        self,
        current_time: datetime,
        periods: List[str] = ['1h', '24h', '7d', '30d']
    ) -> Dict[str, Dict[str, Any]]:
        """获取不同时间段的价格变化"""
        current_data = await self.get_data_at_time(current_time)
        if not current_data:
            return {}
        
        current_price = float(current_data.current_price_usd)
        changes = {}
        
        for period in periods:
            # 计算时间偏移
            if period == '1h':
                past_time = current_time - timedelta(hours=1)
            elif period == '24h':
                past_time = current_time - timedelta(hours=24)
            elif period == '7d':
                past_time = current_time - timedelta(days=7)
            elif period == '30d':
                past_time = current_time - timedelta(days=30)
            else:
                continue
            
            past_data = await self.get_data_at_time(past_time)
            if past_data:
                past_price = float(past_data.current_price_usd)
                price_change = current_price - past_price
                price_change_percentage = (price_change / past_price) * 100 if past_price != 0 else 0
                
                changes[period] = {
                    'price_change': price_change,
                    'price_change_percentage': price_change_percentage,
                    'past_price': past_price,
                    'current_price': current_price
                }
            else:
                changes[period] = {
                    'price_change': None,
                    'price_change_percentage': None,
                    'past_price': None,
                    'current_price': current_price
                }
        
        return changes
    
    async def get_data_at_time(self, target_time: datetime, tolerance_minutes: int = 30) -> Optional[USDTDataModel]:
        """获取指定时间点附近的数据"""
        start_time = target_time - timedelta(minutes=tolerance_minutes)
        end_time = target_time + timedelta(minutes=tolerance_minutes)
        
        result = await self.db.execute(
            select(USDTDataModel)
            .where(
                and_(
                    USDTDataModel.timestamp >= start_time,
                    USDTDataModel.timestamp <= end_time
                )
            )
            .order_by(func.abs(func.extract('epoch', USDTDataModel.timestamp - target_time)))
            .limit(1)
        )
        
        return result.scalar_one_or_none()
    
    async def get_anomaly_data(
        self,
        start_time: datetime,
        end_time: datetime,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[USDTDataModel], int]:
        """获取异常数据"""
        conditions = [
            USDTDataModel.timestamp >= start_time,
            USDTDataModel.timestamp <= end_time,
            USDTDataModel.is_anomaly == True
        ]
        
        # 获取总数
        count_query = select(func.count(USDTDataModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = (
            select(USDTDataModel)
            .where(and_(*conditions))
            .order_by(desc(USDTDataModel.timestamp))
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        data = result.scalars().all()
        
        return data, total
    
    async def mark_as_anomaly(self, data_id: str, is_anomaly: bool = True) -> bool:
        """标记数据为异常"""
        result = await self.db.execute(
            select(USDTDataModel).where(USDTDataModel.id == data_id)
        )
        data = result.scalar_one_or_none()
        
        if data:
            data.is_anomaly = is_anomaly
            await self.db.commit()
            return True
        
        return False
    
    async def get_data_sources(self) -> List[str]:
        """获取所有数据源"""
        result = await self.db.execute(
            select(USDTDataModel.data_source).distinct()
        )
        return [row[0] for row in result.fetchall()]
    
    async def get_data_quality_metrics(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取数据质量指标"""
        conditions = [
            USDTDataModel.timestamp >= start_time,
            USDTDataModel.timestamp <= end_time
        ]
        
        # 基本统计
        result = await self.db.execute(
            select(
                func.count(USDTDataModel.id).label('total_count'),
                func.count(USDTDataModel.id).filter(USDTDataModel.is_anomaly == True).label('anomaly_count'),
                func.count(USDTDataModel.id).filter(USDTDataModel.confidence_score >= 0.8).label('high_quality_count'),
                func.avg(USDTDataModel.confidence_score).label('avg_confidence'),
                func.count(USDTDataModel.data_source).label('source_count')
            ).where(and_(*conditions))
        )
        
        stats = result.first()
        
        if not stats or stats.total_count == 0:
            return {
                'total_data_points': 0,
                'anomaly_rate': 0,
                'high_quality_rate': 0,
                'avg_confidence_score': 0,
                'completeness_score': 0
            }
        
        # 数据源分布
        source_result = await self.db.execute(
            select(
                USDTDataModel.data_source,
                func.count(USDTDataModel.id).label('count')
            )
            .where(and_(*conditions))
            .group_by(USDTDataModel.data_source)
        )
        
        source_distribution = {row.data_source: row.count for row in source_result.fetchall()}
        
        return {
            'total_data_points': stats.total_count,
            'anomaly_count': stats.anomaly_count,
            'anomaly_rate': stats.anomaly_count / stats.total_count,
            'high_quality_count': stats.high_quality_count,
            'high_quality_rate': stats.high_quality_count / stats.total_count,
            'avg_confidence_score': float(stats.avg_confidence) if stats.avg_confidence else 0,
            'source_distribution': source_distribution,
            'completeness_score': 1.0  # 简化计算，实际应该基于预期数据点数量
        }
    
    async def cleanup_old_data(self, before_date: datetime, dry_run: bool = True) -> int:
        """清理旧数据"""
        # 计算要删除的数据量
        count_result = await self.db.execute(
            select(func.count(USDTDataModel.id)).where(USDTDataModel.created_at < before_date)
        )
        count = count_result.scalar()
        
        if not dry_run and count > 0:
            # 执行删除
            await self.db.execute(
                USDTDataModel.__table__.delete().where(USDTDataModel.created_at < before_date)
            )
            await self.db.commit()
        
        return count
    
    async def get_chart_data(
        self,
        start_time: datetime,
        end_time: datetime,
        interval_minutes: int = 60,
        source: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取图表数据"""
        conditions = [
            USDTDataModel.timestamp >= start_time,
            USDTDataModel.timestamp <= end_time
        ]
        
        if source:
            conditions.append(USDTDataModel.data_source == source)
        
        # 按时间间隔分组聚合数据
        query = (
            select(
                func.date_trunc('hour', USDTDataModel.timestamp).label('time_bucket'),
                func.avg(USDTDataModel.current_price_usd).label('avg_price'),
                func.min(USDTDataModel.current_price_usd).label('min_price'),
                func.max(USDTDataModel.current_price_usd).label('max_price'),
                func.avg(USDTDataModel.volume_24h).label('avg_volume'),
                func.count(USDTDataModel.id).label('data_points')
            )
            .where(and_(*conditions))
            .group_by('time_bucket')
            .order_by('time_bucket')
        )
        
        result = await self.db.execute(query)
        
        chart_data = []
        for row in result.fetchall():
            chart_data.append({
                'timestamp': row.time_bucket,
                'price': float(row.avg_price) if row.avg_price else None,
                'min_price': float(row.min_price) if row.min_price else None,
                'max_price': float(row.max_price) if row.max_price else None,
                'volume': int(row.avg_volume) if row.avg_volume else None,
                'data_points': row.data_points
            })
        
        return chart_data
