"""
管理员服务
"""
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from app.core.exceptions import AppException
from app.infrastructure.repositories.user_repository import UserRepository
from app.infrastructure.repositories.usdt_repository import USDTDataRepository


class AdminService:
    """管理员服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.user_repository = UserRepository(db)
        self.usdt_repository = USDTDataRepository(db)
    
    async def get_dashboard_data(self) -> Dict[str, Any]:
        """获取管理员仪表板数据"""
        try:
            # 获取用户统计
            total_users = await self.user_repository.count()
            active_users = await self.user_repository.get_active_users_count()
            verified_users = await self.user_repository.get_verified_users_count()
            locked_users = await self.user_repository.get_locked_users_count()
            
            # 获取数据统计
            end_time = datetime.utcnow()
            start_time_24h = end_time - timedelta(hours=24)
            start_time_7d = end_time - timedelta(days=7)
            
            data_stats_24h = await self.usdt_repository.get_price_statistics(
                start_time_24h, end_time
            )
            
            data_stats_7d = await self.usdt_repository.get_price_statistics(
                start_time_7d, end_time
            )
            
            # 获取数据质量指标
            quality_metrics = await self.usdt_repository.get_data_quality_metrics(
                start_time_7d, end_time
            )
            
            return {
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "verified": verified_users,
                    "locked": locked_users,
                    "verification_rate": verified_users / total_users if total_users > 0 else 0
                },
                "data": {
                    "total_data_points_24h": data_stats_24h['count'],
                    "total_data_points_7d": data_stats_7d['count'],
                    "avg_price_24h": data_stats_24h['avg_price'],
                    "volatility_24h": data_stats_24h['volatility'],
                    "data_sources": await self.usdt_repository.get_data_sources()
                },
                "quality": {
                    "overall_score": quality_metrics['avg_confidence_score'],
                    "anomaly_rate": quality_metrics['anomaly_rate'],
                    "completeness": quality_metrics['completeness_score']
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            raise AppException(
                error_code="DASHBOARD_DATA_ERROR",
                message="获取仪表板数据失败",
                details={"error": str(e)}
            )
    
    async def get_user_stats(self, period: str = "30d") -> Dict[str, Any]:
        """获取用户统计数据"""
        try:
            end_time = datetime.utcnow()
            start_time = self._parse_period(period, end_time)
            
            # 基础统计
            total_users = await self.user_repository.count()
            active_users = await self.user_repository.get_active_users_count()
            verified_users = await self.user_repository.get_verified_users_count()
            
            # 时间段内新用户
            from app.domain.models.user import User as UserModel
            
            new_users_result = await self.db.execute(
                select(func.count(UserModel.id))
                .where(UserModel.created_at >= start_time)
            )
            new_users = new_users_result.scalar()
            
            # 按日期分组的新用户统计
            daily_new_users_result = await self.db.execute(
                select(
                    func.date(UserModel.created_at).label('date'),
                    func.count(UserModel.id).label('count')
                )
                .where(UserModel.created_at >= start_time)
                .group_by(func.date(UserModel.created_at))
                .order_by(func.date(UserModel.created_at))
            )
            
            daily_stats = [
                {"date": row.date.isoformat(), "new_users": row.count}
                for row in daily_new_users_result.fetchall()
            ]
            
            return {
                "summary": {
                    "total_users": total_users,
                    "active_users": active_users,
                    "verified_users": verified_users,
                    "new_users_period": new_users,
                    "growth_rate": new_users / total_users if total_users > 0 else 0
                },
                "daily_stats": daily_stats,
                "period": period,
                "start_date": start_time.isoformat(),
                "end_date": end_time.isoformat()
            }
            
        except Exception as e:
            raise AppException(
                error_code="USER_STATS_ERROR",
                message="获取用户统计失败",
                details={"error": str(e)}
            )
    
    async def get_user_activities(
        self,
        user_id: Optional[UUID] = None,
        activity_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        offset: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户活动日志"""
        try:
            from app.domain.models.user import UserActivity, User as UserModel
            
            # 构建查询条件
            conditions = []
            
            if user_id:
                conditions.append(UserActivity.user_id == user_id)
            
            if activity_type:
                conditions.append(UserActivity.action.ilike(f"%{activity_type}%"))
            
            if start_date:
                conditions.append(UserActivity.created_at >= start_date)
            
            if end_date:
                conditions.append(UserActivity.created_at <= end_date)
            
            # 获取总数
            count_query = select(func.count(UserActivity.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()
            
            # 获取活动数据
            query = (
                select(UserActivity, UserModel.username, UserModel.email)
                .join(UserModel, UserActivity.user_id == UserModel.id)
            )
            
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(UserActivity.created_at.desc()).offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            
            activities = []
            for row in result.fetchall():
                activity, username, email = row
                activities.append({
                    "id": str(activity.id),
                    "user_id": str(activity.user_id),
                    "username": username,
                    "email": email,
                    "action": activity.action,
                    "resource": activity.resource,
                    "details": activity.details,
                    "ip_address": activity.ip_address,
                    "user_agent": activity.user_agent,
                    "created_at": activity.created_at.isoformat()
                })
            
            return activities, total
            
        except Exception as e:
            raise AppException(
                error_code="USER_ACTIVITIES_ERROR",
                message="获取用户活动失败",
                details={"error": str(e)}
            )
    
    async def get_alert_stats(self, period: str = "7d") -> Dict[str, Any]:
        """获取告警统计数据"""
        try:
            # TODO: 实现告警统计逻辑
            # 这里需要告警模型和仓储
            
            return {
                "total_alerts": 0,
                "active_alerts": 0,
                "triggered_alerts": 0,
                "alert_types": {},
                "period": period
            }
            
        except Exception as e:
            raise AppException(
                error_code="ALERT_STATS_ERROR",
                message="获取告警统计失败",
                details={"error": str(e)}
            )
    
    async def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        try:
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=7)
            
            quality_metrics = await self.usdt_repository.get_data_quality_metrics(
                start_time, end_time
            )
            
            return {
                "period": "7d",
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "metrics": quality_metrics,
                "recommendations": self._generate_quality_recommendations(quality_metrics)
            }
            
        except Exception as e:
            raise AppException(
                error_code="DATA_QUALITY_ERROR",
                message="获取数据质量报告失败",
                details={"error": str(e)}
            )
    
    async def cleanup_old_data(self, days: int, dry_run: bool = True) -> Dict[str, Any]:
        """清理旧数据"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # 计算要删除的数据量
            deleted_count = await self.usdt_repository.cleanup_old_data(
                cutoff_date, dry_run=dry_run
            )
            
            return {
                "cutoff_date": cutoff_date.isoformat(),
                "deleted_count": deleted_count,
                "dry_run": dry_run,
                "status": "completed" if not dry_run else "preview"
            }
            
        except Exception as e:
            raise AppException(
                error_code="DATA_CLEANUP_ERROR",
                message="数据清理失败",
                details={"error": str(e)}
            )
    
    async def get_security_audit(self, period: str = "7d") -> Dict[str, Any]:
        """获取安全审计报告"""
        try:
            end_time = datetime.utcnow()
            start_time = self._parse_period(period, end_time)
            
            from app.domain.models.user import UserActivity
            
            # 获取安全相关活动
            security_activities = await self.db.execute(
                select(
                    UserActivity.action,
                    func.count(UserActivity.id).label('count')
                )
                .where(
                    and_(
                        UserActivity.created_at >= start_time,
                        UserActivity.action.in_([
                            'login_success', 'login_failed', 'password_changed',
                            'password_reset_requested', 'email_verified'
                        ])
                    )
                )
                .group_by(UserActivity.action)
            )
            
            activity_stats = {
                row.action: row.count for row in security_activities.fetchall()
            }
            
            # 计算安全指标
            total_logins = activity_stats.get('login_success', 0) + activity_stats.get('login_failed', 0)
            failed_login_rate = activity_stats.get('login_failed', 0) / total_logins if total_logins > 0 else 0
            
            return {
                "period": period,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "metrics": {
                    "total_login_attempts": total_logins,
                    "failed_login_rate": failed_login_rate,
                    "password_changes": activity_stats.get('password_changed', 0),
                    "password_resets": activity_stats.get('password_reset_requested', 0),
                    "email_verifications": activity_stats.get('email_verified', 0)
                },
                "activity_breakdown": activity_stats,
                "security_score": self._calculate_security_score(activity_stats)
            }
            
        except Exception as e:
            raise AppException(
                error_code="SECURITY_AUDIT_ERROR",
                message="获取安全审计失败",
                details={"error": str(e)}
            )
    
    async def get_performance_metrics(self, period: str = "24h") -> Dict[str, Any]:
        """获取性能指标"""
        try:
            # TODO: 实现性能指标收集
            # 这里应该从监控系统获取性能数据
            
            return {
                "period": period,
                "response_times": {
                    "avg": 150,  # ms
                    "p95": 300,
                    "p99": 500
                },
                "throughput": {
                    "requests_per_second": 100,
                    "data_points_per_minute": 2
                },
                "errors": {
                    "error_rate": 0.01,
                    "total_errors": 5
                }
            }
            
        except Exception as e:
            raise AppException(
                error_code="PERFORMANCE_METRICS_ERROR",
                message="获取性能指标失败",
                details={"error": str(e)}
            )
    
    async def broadcast_notification(self, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送广播通知"""
        try:
            # TODO: 实现通知发送逻辑
            # 这里应该通过WebSocket或其他方式发送通知
            
            return {
                "notification_id": "notif_123456",
                "title": notification_data.get("title"),
                "message": notification_data.get("message"),
                "sent_to": "all_users",
                "sent_at": datetime.utcnow().isoformat(),
                "status": "sent"
            }
            
        except Exception as e:
            raise AppException(
                error_code="NOTIFICATION_BROADCAST_ERROR",
                message="发送广播通知失败",
                details={"error": str(e)}
            )
    
    async def get_export_tasks(
        self,
        status_filter: Optional[str] = None,
        offset: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取导出任务列表"""
        try:
            # TODO: 实现导出任务查询
            # 这里需要导出任务模型和仓储
            
            tasks = []
            total = 0
            
            return tasks, total
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_TASKS_ERROR",
                message="获取导出任务失败",
                details={"error": str(e)}
            )
    
    async def delete_export_task(self, export_id: UUID) -> bool:
        """删除导出任务"""
        try:
            # TODO: 实现导出任务删除
            return True
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_DELETE_ERROR",
                message="删除导出任务失败",
                details={"error": str(e)}
            )
    
    async def schedule_maintenance(self, maintenance_data: Dict[str, Any]) -> Dict[str, Any]:
        """安排系统维护"""
        try:
            # TODO: 实现维护计划功能
            
            return {
                "maintenance_id": "maint_123456",
                "start_time": maintenance_data.get("start_time"),
                "end_time": maintenance_data.get("end_time"),
                "description": maintenance_data.get("description"),
                "status": "scheduled",
                "created_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            raise AppException(
                error_code="MAINTENANCE_SCHEDULE_ERROR",
                message="安排维护失败",
                details={"error": str(e)}
            )
    
    def _parse_period(self, period: str, end_time: datetime) -> datetime:
        """解析时间周期"""
        period_map = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        
        if period not in period_map:
            raise AppException(
                error_code="INVALID_PERIOD",
                message=f"无效的时间周期: {period}"
            )
        
        return end_time - period_map[period]
    
    def _generate_quality_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """生成数据质量建议"""
        recommendations = []
        
        if metrics['anomaly_rate'] > 0.05:
            recommendations.append("异常数据比例较高，建议检查数据源配置")
        
        if metrics['avg_confidence_score'] < 0.8:
            recommendations.append("数据可信度较低，建议增加数据验证规则")
        
        if metrics['completeness_score'] < 0.9:
            recommendations.append("数据完整性不足，建议检查数据收集流程")
        
        if not recommendations:
            recommendations.append("数据质量良好，继续保持")
        
        return recommendations
    
    def _calculate_security_score(self, activity_stats: Dict[str, int]) -> float:
        """计算安全评分"""
        try:
            total_logins = activity_stats.get('login_success', 0) + activity_stats.get('login_failed', 0)
            failed_rate = activity_stats.get('login_failed', 0) / total_logins if total_logins > 0 else 0
            
            # 基础评分
            score = 1.0
            
            # 失败登录率影响
            if failed_rate > 0.1:  # 失败率超过10%
                score -= 0.3
            elif failed_rate > 0.05:  # 失败率超过5%
                score -= 0.1
            
            # 密码重置频率影响
            reset_rate = activity_stats.get('password_reset_requested', 0) / max(1, activity_stats.get('login_success', 1))
            if reset_rate > 0.1:
                score -= 0.2
            
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.5  # 默认中等安全评分
