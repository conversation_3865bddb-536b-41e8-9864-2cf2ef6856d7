import { apiClient, PaginatedResponse } from './client'
import type {
  USDTData,
  USDTStats,
  USDTAlert,
  USDTAlertCreate,
  USDTExportRequest,
  USDTHistoryQuery,
  USDTChartData
} from '../../types/usdt'

export class USDTApi {
  // 获取当前USDT数据
  async getCurrentData() {
    return apiClient.get<USDTData>('/usdt/current')
  }

  // 获取历史数据
  async getHistoryData(params: USDTHistoryQuery & { page?: number; size?: number }) {
    return apiClient.get<PaginatedResponse<USDTData>>('/usdt/history', { params })
  }

  // 获取统计数据
  async getStats(period: string = '24h') {
    return apiClient.get<USDTStats>('/usdt/stats', { params: { period } })
  }

  // 获取图表数据
  async getChartData(period: string, interval: string) {
    return apiClient.get<USDTChartData[]>('/usdt/price-chart', {
      params: { period, interval }
    })
  }

  // 创建价格告警
  async createAlert(alertData: USDTAlertCreate) {
    return apiClient.post<USDTAlert>('/usdt/alerts', alertData)
  }

  // 获取用户告警列表
  async getUserAlerts(params: { page?: number; size?: number; is_active?: boolean }) {
    return apiClient.get<PaginatedResponse<USDTAlert>>('/usdt/alerts', { params })
  }

  // 更新告警
  async updateAlert(alertId: string, alertData: Partial<USDTAlertCreate>) {
    return apiClient.put<USDTAlert>(`/usdt/alerts/${alertId}`, alertData)
  }

  // 删除告警
  async deleteAlert(alertId: string) {
    return apiClient.delete(`/usdt/alerts/${alertId}`)
  }

  // 切换告警状态
  async toggleAlert(alertId: string, isActive: boolean) {
    return apiClient.patch<USDTAlert>(`/usdt/alerts/${alertId}`, { is_active: isActive })
  }

  // 导出数据
  async exportData(exportRequest: USDTExportRequest) {
    return apiClient.post<{ export_id: string; message: string }>('/usdt/export', exportRequest)
  }

  // 获取导出状态
  async getExportStatus(exportId: string) {
    return apiClient.get<{
      export_id: string
      status: string
      progress: number
      created_at: string
      completed_at?: string
      file_size?: number
      error_message?: string
    }>(`/usdt/export/${exportId}/status`)
  }

  // 下载导出文件
  async downloadExport(exportId: string, filename?: string) {
    return apiClient.download(`/usdt/export/${exportId}/download`, filename)
  }

  // 获取数据源列表
  async getDataSources() {
    return apiClient.get<string[]>('/usdt/sources')
  }

  // 获取数据质量报告
  async getDataQuality(period: string = '7d') {
    return apiClient.get<{
      period: string
      total_data_points: number
      anomaly_count: number
      anomaly_rate: number
      completeness_score: number
      accuracy_score: number
      source_distribution: Record<string, number>
    }>('/usdt/data-quality', { params: { period } })
  }

  // 手动触发数据收集
  async triggerDataCollection() {
    return apiClient.post<{ message: string }>('/usdt/collect')
  }

  // 获取实时价格流
  async subscribeToRealTimeData(callback: (data: USDTData) => void) {
    // 这里可以实现WebSocket连接
    // 暂时使用轮询方式
    const interval = setInterval(async () => {
      try {
        const response = await this.getCurrentData()
        callback(response.data)
      } catch (error) {
        console.error('Failed to fetch real-time data:', error)
      }
    }, 30000) // 30秒轮询

    return () => clearInterval(interval)
  }
}

export const usdtApi = new USDTApi()
