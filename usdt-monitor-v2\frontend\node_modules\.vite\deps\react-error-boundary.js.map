{"version": 3, "sources": ["../../react-error-boundary/dist/react-error-boundary.development.esm.js"], "sourcesContent": ["'use client';\nimport { createContext, Component, createElement, useContext, useState, useMemo, forwardRef } from 'react';\n\nconst ErrorBoundaryContext = createContext(null);\n\nconst initialState = {\n  didCatch: false,\n  error: null\n};\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.resetErrorBoundary = this.resetErrorBoundary.bind(this);\n    this.state = initialState;\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      didCatch: true,\n      error\n    };\n  }\n  resetErrorBoundary() {\n    const {\n      error\n    } = this.state;\n    if (error !== null) {\n      var _this$props$onReset, _this$props;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {\n        args,\n        reason: \"imperative-api\"\n      });\n      this.setState(initialState);\n    }\n  }\n  componentDidCatch(error, info) {\n    var _this$props$onError, _this$props2;\n    (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);\n  }\n  componentDidUpdate(prevProps, prevState) {\n    const {\n      didCatch\n    } = this.state;\n    const {\n      resetKeys\n    } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {\n      var _this$props$onReset2, _this$props3;\n      (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\"\n      });\n      this.setState(initialState);\n    }\n  }\n  render() {\n    const {\n      children,\n      fallbackRender,\n      FallbackComponent,\n      fallback\n    } = this.props;\n    const {\n      didCatch,\n      error\n    } = this.state;\n    let childToRender = children;\n    if (didCatch) {\n      const props = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary\n      };\n      if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else if (fallback !== undefined) {\n        childToRender = fallback;\n      } else {\n        {\n          console.error(\"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\");\n        }\n        throw error;\n      }\n    }\n    return createElement(ErrorBoundaryContext.Provider, {\n      value: {\n        didCatch,\n        error,\n        resetErrorBoundary: this.resetErrorBoundary\n      }\n    }, childToRender);\n  }\n}\nfunction hasArrayChanged() {\n  let a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  let b = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  return a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]));\n}\n\nfunction assertErrorBoundaryContext(value) {\n  if (value == null || typeof value.didCatch !== \"boolean\" || typeof value.resetErrorBoundary !== \"function\") {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n}\n\nfunction useErrorBoundary() {\n  const context = useContext(ErrorBoundaryContext);\n  assertErrorBoundaryContext(context);\n  const [state, setState] = useState({\n    error: null,\n    hasError: false\n  });\n  const memoized = useMemo(() => ({\n    resetBoundary: () => {\n      context.resetErrorBoundary();\n      setState({\n        error: null,\n        hasError: false\n      });\n    },\n    showBoundary: error => setState({\n      error,\n      hasError: true\n    })\n  }), [context.resetErrorBoundary]);\n  if (state.hasError) {\n    throw state.error;\n  }\n  return memoized;\n}\n\nfunction withErrorBoundary(component, errorBoundaryProps) {\n  const Wrapped = forwardRef((props, ref) => createElement(ErrorBoundary, errorBoundaryProps, createElement(component, {\n    ...props,\n    ref\n  })));\n\n  // Format for display in DevTools\n  const name = component.displayName || component.name || \"Unknown\";\n  Wrapped.displayName = \"withErrorBoundary(\".concat(name, \")\");\n  return Wrapped;\n}\n\nexport { ErrorBoundary, ErrorBoundaryContext, useErrorBoundary, withErrorBoundary };\n"], "mappings": ";;;;;;;;;AACA,mBAAmG;AAEnG,IAAM,2BAAuB,4BAAc,IAAI;AAE/C,IAAM,eAAe;AAAA,EACnB,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAM,gBAAN,cAA4B,uBAAU;AAAA,EACpC,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO,yBAAyB,OAAO;AACrC,WAAO;AAAA,MACL,UAAU;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,QAAI,UAAU,MAAM;AAClB,UAAI,qBAAqB;AACzB,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AACA,OAAC,uBAAuB,cAAc,KAAK,OAAO,aAAa,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,KAAK,aAAa;AAAA,QACrJ;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,SAAS,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO,MAAM;AAC7B,QAAI,qBAAqB;AACzB,KAAC,uBAAuB,eAAe,KAAK,OAAO,aAAa,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,KAAK,cAAc,OAAO,IAAI;AAAA,EACtK;AAAA,EACA,mBAAmB,WAAW,WAAW;AACvC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AAOT,QAAI,YAAY,UAAU,UAAU,QAAQ,gBAAgB,UAAU,WAAW,SAAS,GAAG;AAC3F,UAAI,sBAAsB;AAC1B,OAAC,wBAAwB,eAAe,KAAK,OAAO,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,cAAc;AAAA,QAC1J,MAAM;AAAA,QACN,MAAM,UAAU;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,SAAS,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACZ,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA,oBAAoB,KAAK;AAAA,MAC3B;AACA,UAAI,OAAO,mBAAmB,YAAY;AACxC,wBAAgB,eAAe,KAAK;AAAA,MACtC,WAAW,mBAAmB;AAC5B,4BAAgB,4BAAc,mBAAmB,KAAK;AAAA,MACxD,WAAW,aAAa,QAAW;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL;AACE,kBAAQ,MAAM,4FAA4F;AAAA,QAC5G;AACA,cAAM;AAAA,MACR;AAAA,IACF;AACA,eAAO,4BAAc,qBAAqB,UAAU;AAAA,MAClD,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,oBAAoB,KAAK;AAAA,MAC3B;AAAA,IACF,GAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,kBAAkB;AACzB,MAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC7E,MAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC7E,SAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC,MAAM,UAAU,CAAC,OAAO,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;AACpF;AAEA,SAAS,2BAA2B,OAAO;AACzC,MAAI,SAAS,QAAQ,OAAO,MAAM,aAAa,aAAa,OAAO,MAAM,uBAAuB,YAAY;AAC1G,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACF;AAEA,SAAS,mBAAmB;AAC1B,QAAM,cAAU,yBAAW,oBAAoB;AAC/C,6BAA2B,OAAO;AAClC,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS;AAAA,IACjC,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,eAAW,sBAAQ,OAAO;AAAA,IAC9B,eAAe,MAAM;AACnB,cAAQ,mBAAmB;AAC3B,eAAS;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,IACA,cAAc,WAAS,SAAS;AAAA,MAC9B;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,IAAI,CAAC,QAAQ,kBAAkB,CAAC;AAChC,MAAI,MAAM,UAAU;AAClB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,WAAW,oBAAoB;AACxD,QAAM,cAAU,yBAAW,CAAC,OAAO,YAAQ,4BAAc,eAAe,wBAAoB,4BAAc,WAAW;AAAA,IACnH,GAAG;AAAA,IACH;AAAA,EACF,CAAC,CAAC,CAAC;AAGH,QAAM,OAAO,UAAU,eAAe,UAAU,QAAQ;AACxD,UAAQ,cAAc,qBAAqB,OAAO,MAAM,GAAG;AAC3D,SAAO;AACT;", "names": []}