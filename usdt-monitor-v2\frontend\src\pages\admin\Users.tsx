import React, { useState } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Input,
  Select,
  Modal,
  Form,
  Switch,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
  Tooltip,
  Badge,
  Drawer
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import type { User } from '../../types/auth'

const { Title, Text } = Typography
const { Option } = Select
const { Search } = Input

// 模拟用户API
const userApi = {
  getUsers: async (params: any) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      data: {
        items: [
          {
            id: '1',
            email: '<EMAIL>',
            username: 'admin',
            full_name: '系统管理员',
            avatar_url: '',
            phone: '+86 138 0013 8000',
            is_active: true,
            is_verified: true,
            is_superuser: true,
            last_login: '2024-01-15T10:30:00Z',
            created_at: '2024-01-01T00:00:00Z'
          },
          {
            id: '2',
            email: '<EMAIL>',
            username: 'user001',
            full_name: '普通用户',
            avatar_url: '',
            phone: '+86 139 0013 9000',
            is_active: true,
            is_verified: true,
            is_superuser: false,
            last_login: '2024-01-15T09:15:00Z',
            created_at: '2024-01-02T00:00:00Z'
          }
        ],
        total: 2,
        page: 1,
        size: 20
      }
    }
  },
  createUser: async (userData: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { data: { ...userData, id: Date.now().toString() } }
  },
  updateUser: async (id: string, userData: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { data: { ...userData, id } }
  },
  deleteUser: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { data: { success: true } }
  }
}

export default function Users() {
  const queryClient = useQueryClient()
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [roleFilter, setRoleFilter] = useState<string>('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 获取用户列表
  const {
    data: usersData,
    isLoading
  } = useQuery({
    queryKey: ['admin', 'users', pagination.current, pagination.pageSize, searchText, statusFilter, roleFilter],
    queryFn: () => userApi.getUsers({
      page: pagination.current,
      size: pagination.pageSize,
      search: searchText,
      status: statusFilter,
      role: roleFilter
    })
  })

  // 创建用户
  const createUserMutation = useMutation({
    mutationFn: userApi.createUser,
    onSuccess: () => {
      message.success('用户创建成功')
      setIsModalVisible(false)
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] })
    },
    onError: () => {
      message.error('用户创建失败')
    }
  })

  // 更新用户
  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => userApi.updateUser(id, data),
    onSuccess: () => {
      message.success('用户更新成功')
      setIsModalVisible(false)
      setEditingUser(null)
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] })
    },
    onError: () => {
      message.error('用户更新失败')
    }
  })

  // 删除用户
  const deleteUserMutation = useMutation({
    mutationFn: userApi.deleteUser,
    onSuccess: () => {
      message.success('用户删除成功')
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] })
    },
    onError: () => {
      message.error('用户删除失败')
    }
  })

  const users = usersData?.data?.items || []
  const total = usersData?.data?.total || 0

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (editingUser) {
      updateUserMutation.mutate({ id: editingUser.id, data: values })
    } else {
      createUserMutation.mutate(values)
    }
  }

  // 打开编辑模态框
  const handleEdit = (user: User) => {
    setEditingUser(user)
    form.setFieldsValue({
      email: user.email,
      username: user.username,
      full_name: user.full_name,
      phone: user.phone,
      is_active: user.is_active,
      is_superuser: user.is_superuser
    })
    setIsModalVisible(true)
  }

  // 查看用户详情
  const handleViewUser = (user: User) => {
    setSelectedUser(user)
    setIsDrawerVisible(true)
  }

  // 关闭模态框
  const handleCancel = () => {
    setIsModalVisible(false)
    setEditingUser(null)
    form.resetFields()
  }

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户',
      key: 'user',
      width: 200,
      render: (_, record: User) => (
        <div className="flex items-center space-x-3">
          <Avatar
            size={40}
            src={record.avatar_url}
            icon={<UserOutlined />}
          />
          <div>
            <div className="font-medium">{record.full_name}</div>
            <Text type="secondary" className="text-sm">@{record.username}</Text>
          </div>
        </div>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (email: string, record: User) => (
        <div>
          <div>{email}</div>
          {record.is_verified ? (
            <Tag color="green">已验证</Tag>
          ) : (
            <Tag color="orange">未验证</Tag>
          )}
        </div>
      )
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      render: (phone: string) => phone || '-'
    },
    {
      title: '角色',
      key: 'role',
      width: 100,
      render: (_, record: User) => (
        <Tag color={record.is_superuser ? 'purple' : 'blue'}>
          {record.is_superuser ? '管理员' : '普通用户'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'status',
      width: 100,
      render: (isActive: boolean) => (
        <Badge
          status={isActive ? 'success' : 'error'}
          text={isActive ? '正常' : '禁用'}
        />
      )
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      width: 150,
      render: (time: string) => {
        if (!time) return <Text type="secondary">从未登录</Text>
        return (
          <div>
            <div>{dayjs(time).format('MM-DD HH:mm')}</div>
            <Text type="secondary" className="text-xs">
              {dayjs(time).fromNow()}
            </Text>
          </div>
        )
      }
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('YYYY-MM-DD')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(time).fromNow()}
          </Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: User) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewUser(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_active ? '禁用' : '启用'}>
            <Button
              type="link"
              size="small"
              icon={record.is_active ? <LockOutlined /> : <UnlockOutlined />}
              onClick={() => {
                updateUserMutation.mutate({
                  id: record.id,
                  data: { is_active: !record.is_active }
                })
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => deleteUserMutation.mutate(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  // 计算统计数据
  const statistics = {
    total: users.length,
    active: users.filter(user => user.is_active).length,
    verified: users.filter(user => user.is_verified).length,
    admins: users.filter(user => user.is_superuser).length
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            用户管理
          </Title>
          <Text type="secondary">
            管理系统用户和权限设置
          </Text>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          添加用户
        </Button>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="总用户数"
              value={statistics.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="活跃用户"
              value={statistics.active}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="已验证"
              value={statistics.verified}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="管理员"
              value={statistics.admins}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card size="small">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} lg={6}>
            <Search
              placeholder="搜索用户..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={setSearchText}
              allowClear
            />
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Select
              placeholder="状态筛选"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">正常</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Select
              placeholder="角色筛选"
              value={roleFilter}
              onChange={setRoleFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="admin">管理员</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 用户列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination({ current: page, pageSize: pageSize || 20, total })
            }
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建/编辑用户模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="full_name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="is_active"
                label="账户状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="正常" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="is_superuser"
                label="管理员权限"
                valuePropName="checked"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createUserMutation.isPending || updateUserMutation.isPending}
              >
                {editingUser ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户详情抽屉 */}
      <Drawer
        title="用户详情"
        placement="right"
        onClose={() => setIsDrawerVisible(false)}
        open={isDrawerVisible}
        width={400}
      >
        {selectedUser && (
          <div className="space-y-6">
            {/* 用户头像和基本信息 */}
            <div className="text-center">
              <Avatar
                size={80}
                src={selectedUser.avatar_url}
                icon={<UserOutlined />}
                className="mb-4"
              />
              <div>
                <Title level={4} className="mb-1">{selectedUser.full_name}</Title>
                <Text type="secondary">@{selectedUser.username}</Text>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="space-y-4">
              <div>
                <Text type="secondary" className="block">邮箱地址</Text>
                <Text>{selectedUser.email}</Text>
                {selectedUser.is_verified && (
                  <Tag color="green" className="ml-2">已验证</Tag>
                )}
              </div>

              <div>
                <Text type="secondary" className="block">手机号码</Text>
                <Text>{selectedUser.phone || '未设置'}</Text>
              </div>

              <div>
                <Text type="secondary" className="block">账户状态</Text>
                <Badge
                  status={selectedUser.is_active ? 'success' : 'error'}
                  text={selectedUser.is_active ? '正常' : '禁用'}
                />
              </div>

              <div>
                <Text type="secondary" className="block">用户角色</Text>
                <Tag color={selectedUser.is_superuser ? 'purple' : 'blue'}>
                  {selectedUser.is_superuser ? '管理员' : '普通用户'}
                </Tag>
              </div>

              <div>
                <Text type="secondary" className="block">注册时间</Text>
                <Text>{dayjs(selectedUser.created_at).format('YYYY-MM-DD HH:mm:ss')}</Text>
              </div>

              <div>
                <Text type="secondary" className="block">最后登录</Text>
                <Text>
                  {selectedUser.last_login ?
                    dayjs(selectedUser.last_login).format('YYYY-MM-DD HH:mm:ss') :
                    '从未登录'
                  }
                </Text>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-2">
              <Button
                block
                icon={<EditOutlined />}
                onClick={() => {
                  setIsDrawerVisible(false)
                  handleEdit(selectedUser)
                }}
              >
                编辑用户
              </Button>
              <Button
                block
                icon={selectedUser.is_active ? <LockOutlined /> : <UnlockOutlined />}
                onClick={() => {
                  updateUserMutation.mutate({
                    id: selectedUser.id,
                    data: { is_active: !selectedUser.is_active }
                  })
                  setIsDrawerVisible(false)
                }}
              >
                {selectedUser.is_active ? '禁用账户' : '启用账户'}
              </Button>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  )
}
