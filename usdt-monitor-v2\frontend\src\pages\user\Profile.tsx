import React, { useState } from 'react'
import {
  Card,
  Typography,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Row,
  Col,
  Divider,
  Space,
  Tag,
  Descriptions,
  message,
  Modal,
  Alert
} from 'antd'
import {
  UserOutlined,
  EditOutlined,
  CameraOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { UploadProps } from 'antd'
import { useAuthStore } from '../../stores/authStore'
import { authApi } from '../../services/api/auth'
import type { UserProfileUpdate, ChangePasswordRequest } from '../../types/auth'

const { Title, Text } = Typography
const { TextArea } = Input

export default function Profile() {
  const { user, updateUser } = useAuthStore()
  const queryClient = useQueryClient()
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [isEditing, setIsEditing] = useState(false)
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false)
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar_url)

  // 更新个人资料
  const updateProfileMutation = useMutation({
    mutationFn: (data: UserProfileUpdate) => authApi.updateProfile?.(data) || Promise.resolve({ data: user }),
    onSuccess: (response) => {
      message.success('个人资料更新成功')
      updateUser(response.data)
      setIsEditing(false)
      queryClient.invalidateQueries({ queryKey: ['auth', 'me'] })
    },
    onError: () => {
      message.error('个人资料更新失败')
    }
  })

  // 修改密码
  const changePasswordMutation = useMutation({
    mutationFn: (data: ChangePasswordRequest) => authApi.changePassword(data),
    onSuccess: () => {
      message.success('密码修改成功')
      setIsPasswordModalVisible(false)
      passwordForm.resetFields()
    },
    onError: () => {
      message.error('密码修改失败')
    }
  })

  // 处理个人资料提交
  const handleProfileSubmit = async (values: any) => {
    const updateData: UserProfileUpdate = {
      full_name: values.full_name,
      phone: values.phone,
      bio: values.bio,
      avatar_url: avatarUrl
    }
    updateProfileMutation.mutate(updateData)
  }

  // 处理密码修改提交
  const handlePasswordSubmit = async (values: any) => {
    const passwordData: ChangePasswordRequest = {
      current_password: values.current_password,
      new_password: values.new_password,
      confirm_password: values.confirm_password
    }
    changePasswordMutation.mutate(passwordData)
  }

  // 头像上传配置
  const uploadProps: UploadProps = {
    name: 'avatar',
    listType: 'picture-card',
    className: 'avatar-uploader',
    showUploadList: false,
    action: '/api/v1/upload/avatar',
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    onChange: (info) => {
      if (info.file.status === 'uploading') {
        return
      }
      if (info.file.status === 'done') {
        setAvatarUrl(info.file.response?.data?.url)
        message.success('头像上传成功')
      }
      if (info.file.status === 'error') {
        message.error('头像上传失败')
      }
    }
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false)
    profileForm.setFieldsValue({
      full_name: user?.full_name,
      phone: user?.phone,
      bio: user?.bio
    })
    setAvatarUrl(user?.avatar_url)
  }

  // 初始化表单值
  React.useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        full_name: user.full_name,
        phone: user.phone,
        bio: user.bio
      })
    }
  }, [user, profileForm])

  if (!user) {
    return (
      <div className="p-6">
        <Alert message="用户信息加载失败" type="error" />
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            个人资料
          </Title>
          <Text type="secondary">
            管理您的个人信息和账户设置
          </Text>
        </div>
        {!isEditing && (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => setIsEditing(true)}
          >
            编辑资料
          </Button>
        )}
      </div>

      <Row gutter={[24, 24]}>
        {/* 基本信息 */}
        <Col xs={24} lg={16}>
          <Card title="基本信息">
            {isEditing ? (
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleProfileSubmit}
              >
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="full_name"
                      label="姓名"
                      rules={[
                        { required: true, message: '请输入姓名' },
                        { min: 2, message: '姓名至少2个字符' }
                      ]}
                    >
                      <Input placeholder="请输入姓名" />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="phone"
                      label="手机号码"
                      rules={[
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                      ]}
                    >
                      <Input placeholder="请输入手机号码" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  name="bio"
                  label="个人简介"
                >
                  <TextArea
                    rows={4}
                    placeholder="介绍一下自己..."
                    maxLength={200}
                    showCount
                  />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={updateProfileMutation.isPending}
                    >
                      保存
                    </Button>
                    <Button onClick={handleCancelEdit}>
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            ) : (
              <Descriptions column={1} size="large">
                <Descriptions.Item label="姓名">
                  {user.full_name || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="用户名">
                  {user.username}
                </Descriptions.Item>
                <Descriptions.Item label="邮箱">
                  <Space>
                    <MailOutlined />
                    {user.email}
                    {user.is_verified ? (
                      <Tag color="green">已验证</Tag>
                    ) : (
                      <Tag color="orange">未验证</Tag>
                    )}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="手机号码">
                  <Space>
                    <PhoneOutlined />
                    {user.phone || '未设置'}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="个人简介">
                  {user.bio || '这个人很懒，什么都没有留下...'}
                </Descriptions.Item>
                <Descriptions.Item label="注册时间">
                  <Space>
                    <CalendarOutlined />
                    {dayjs(user.created_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="最后登录">
                  <Space>
                    <CalendarOutlined />
                    {user.last_login ?
                      dayjs(user.last_login).format('YYYY-MM-DD HH:mm:ss') :
                      '从未登录'
                    }
                  </Space>
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        </Col>

        {/* 头像和账户状态 */}
        <Col xs={24} lg={8}>
          <Card title="头像设置">
            <div className="text-center space-y-4">
              <div className="relative inline-block">
                <Avatar
                  size={120}
                  src={avatarUrl}
                  icon={<UserOutlined />}
                  className="border-4 border-gray-200"
                />
                {isEditing && (
                  <Upload {...uploadProps}>
                    <Button
                      type="primary"
                      shape="circle"
                      icon={<CameraOutlined />}
                      className="absolute bottom-0 right-0"
                      size="small"
                    />
                  </Upload>
                )}
              </div>
              <div>
                <Text strong className="block">{user.full_name}</Text>
                <Text type="secondary">@{user.username}</Text>
              </div>
              {isEditing && (
                <Text type="secondary" className="text-sm">
                  点击相机图标更换头像
                </Text>
              )}
            </div>
          </Card>

          <Card title="账户状态" className="mt-6">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Text>账户状态</Text>
                <Tag color={user.is_active ? 'green' : 'red'}>
                  {user.is_active ? '正常' : '已禁用'}
                </Tag>
              </div>
              <div className="flex justify-between items-center">
                <Text>邮箱验证</Text>
                <Tag color={user.is_verified ? 'green' : 'orange'}>
                  {user.is_verified ? '已验证' : '未验证'}
                </Tag>
              </div>
              <div className="flex justify-between items-center">
                <Text>账户类型</Text>
                <Tag color={user.is_superuser ? 'purple' : 'blue'}>
                  {user.is_superuser ? '管理员' : '普通用户'}
                </Tag>
              </div>
            </div>
          </Card>

          <Card title="安全设置" className="mt-6">
            <div className="space-y-3">
              <Button
                block
                icon={<LockOutlined />}
                onClick={() => setIsPasswordModalVisible(true)}
              >
                修改密码
              </Button>
              {!user.is_verified && (
                <Button block type="dashed">
                  验证邮箱
                </Button>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={isPasswordModalVisible}
        onCancel={() => {
          setIsPasswordModalVisible(false)
          passwordForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordSubmit}
        >
          <Form.Item
            name="current_password"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password
              placeholder="请输入当前密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8个字符' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: '密码必须包含大小写字母和数字'
              }
            ]}
            hasFeedback
          >
            <Input.Password
              placeholder="请输入新密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="confirm_password"
            label="确认新密码"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                },
              }),
            ]}
            hasFeedback
          >
            <Input.Password
              placeholder="请再次输入新密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={() => {
                setIsPasswordModalVisible(false)
                passwordForm.resetFields()
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={changePasswordMutation.isPending}
              >
                修改密码
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Divider />

        <Alert
          message="密码安全提示"
          description={
            <ul className="mt-2 space-y-1 text-sm">
              <li>• 密码至少8个字符</li>
              <li>• 必须包含大小写字母和数字</li>
              <li>• 建议包含特殊字符</li>
              <li>• 不要使用常见密码</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </Modal>
    </div>
  )
}
