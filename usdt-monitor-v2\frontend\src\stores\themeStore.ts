import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ThemeMode = 'light' | 'dark' | 'auto'
export type LayoutMode = 'side' | 'top' | 'mix'

interface ThemeState {
  // 主题设置
  theme: ThemeMode
  isDark: boolean
  primaryColor: string
  layoutMode: LayoutMode
  
  // 布局设置
  collapsed: boolean
  fixedHeader: boolean
  fixedSider: boolean
  
  // 操作
  setTheme: (theme: ThemeMode) => void
  setPrimaryColor: (color: string) => void
  setLayoutMode: (mode: LayoutMode) => void
  toggleCollapsed: () => void
  setCollapsed: (collapsed: boolean) => void
  setFixedHeader: (fixed: boolean) => void
  setFixedSider: (fixed: boolean) => void
  initializeTheme: () => void
  resetTheme: () => void
}

// 默认主题配置
const defaultTheme = {
  theme: 'light' as ThemeMode,
  isDark: false,
  primaryColor: '#1890ff',
  layoutMode: 'side' as LayoutMode,
  collapsed: false,
  fixedHeader: true,
  fixedSider: true,
}

// 检测系统主题
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light'
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

// 应用主题到DOM
const applyTheme = (isDark: boolean) => {
  const root = document.documentElement
  if (isDark) {
    root.classList.add('dark')
    root.setAttribute('data-theme', 'dark')
  } else {
    root.classList.remove('dark')
    root.setAttribute('data-theme', 'light')
  }
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      ...defaultTheme,

      // 设置主题模式
      setTheme: (theme: ThemeMode) => {
        let isDark = false
        
        if (theme === 'dark') {
          isDark = true
        } else if (theme === 'auto') {
          isDark = getSystemTheme() === 'dark'
        }
        
        applyTheme(isDark)
        
        set({ theme, isDark })
      },

      // 设置主色调
      setPrimaryColor: (color: string) => {
        set({ primaryColor: color })
      },

      // 设置布局模式
      setLayoutMode: (mode: LayoutMode) => {
        set({ layoutMode: mode })
      },

      // 切换侧边栏折叠状态
      toggleCollapsed: () => {
        set((state) => ({ collapsed: !state.collapsed }))
      },

      // 设置侧边栏折叠状态
      setCollapsed: (collapsed: boolean) => {
        set({ collapsed })
      },

      // 设置固定头部
      setFixedHeader: (fixed: boolean) => {
        set({ fixedHeader: fixed })
      },

      // 设置固定侧边栏
      setFixedSider: (fixed: boolean) => {
        set({ fixedSider: fixed })
      },

      // 初始化主题
      initializeTheme: () => {
        const { theme } = get()
        
        // 监听系统主题变化
        if (theme === 'auto') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
          const handleChange = () => {
            const isDark = mediaQuery.matches
            applyTheme(isDark)
            set({ isDark })
          }
          
          mediaQuery.addEventListener('change', handleChange)
          handleChange() // 初始应用
        } else {
          applyTheme(theme === 'dark')
        }
      },

      // 重置主题
      resetTheme: () => {
        applyTheme(false)
        set(defaultTheme)
      },
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        theme: state.theme,
        primaryColor: state.primaryColor,
        layoutMode: state.layoutMode,
        collapsed: state.collapsed,
        fixedHeader: state.fixedHeader,
        fixedSider: state.fixedSider,
      }),
    }
  )
)
