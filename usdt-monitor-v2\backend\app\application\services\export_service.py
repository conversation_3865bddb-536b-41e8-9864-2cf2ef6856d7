"""
导出服务
"""
import csv
import json
import io
import zipfile
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, BinaryIO
from uuid import UUID, uuid4

import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.exceptions import AppException
from app.domain.schemas.usdt import USDTExportRequest
from app.infrastructure.repositories.usdt_repository import USDTDataRepository


class ExportService:
    """导出服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.usdt_repository = USDTDataRepository(db)
        self.export_tasks: Dict[UUID, Dict[str, Any]] = {}
    
    async def create_export_task(
        self, 
        user_id: UUID, 
        export_request: USDTExportRequest
    ) -> UUID:
        """创建导出任务"""
        try:
            export_id = uuid4()
            
            # 创建任务记录
            task_info = {
                "id": export_id,
                "user_id": user_id,
                "request": export_request.dict(),
                "status": "pending",
                "created_at": datetime.utcnow(),
                "started_at": None,
                "completed_at": None,
                "file_path": None,
                "file_size": 0,
                "error_message": None,
                "progress": 0
            }
            
            self.export_tasks[export_id] = task_info
            
            return export_id
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_TASK_CREATE_ERROR",
                message="创建导出任务失败",
                details={"error": str(e)}
            )
    
    async def process_export_task(self, export_id: UUID) -> None:
        """处理导出任务"""
        try:
            task_info = self.export_tasks.get(export_id)
            if not task_info:
                raise AppException(
                    error_code="EXPORT_TASK_NOT_FOUND",
                    message="导出任务不存在"
                )
            
            # 更新任务状态
            task_info["status"] = "processing"
            task_info["started_at"] = datetime.utcnow()
            task_info["progress"] = 10
            
            # 解析导出请求
            request_data = task_info["request"]
            export_request = USDTExportRequest.parse_obj(request_data)
            
            # 获取数据
            task_info["progress"] = 30
            data_models, total = await self.usdt_repository.get_data_by_time_range(
                start_time=export_request.start_date,
                end_time=export_request.end_date,
                source=None,
                interval_minutes=self._parse_interval(export_request.interval),
                skip=0,
                limit=10000  # 限制导出数量
            )
            
            if not data_models:
                raise AppException(
                    error_code="NO_DATA_TO_EXPORT",
                    message="没有数据可导出"
                )
            
            # 转换数据格式
            task_info["progress"] = 50
            export_data = self._prepare_export_data(data_models, export_request)
            
            # 生成文件
            task_info["progress"] = 70
            file_path, file_size = await self._generate_export_file(
                export_data, export_request, export_id
            )
            
            # 完成任务
            task_info["status"] = "completed"
            task_info["completed_at"] = datetime.utcnow()
            task_info["file_path"] = file_path
            task_info["file_size"] = file_size
            task_info["progress"] = 100
            
        except Exception as e:
            # 更新任务状态为失败
            if export_id in self.export_tasks:
                self.export_tasks[export_id]["status"] = "failed"
                self.export_tasks[export_id]["error_message"] = str(e)
                self.export_tasks[export_id]["completed_at"] = datetime.utcnow()
            
            raise AppException(
                error_code="EXPORT_PROCESSING_ERROR",
                message="处理导出任务失败",
                details={"error": str(e)}
            )
    
    async def get_export_status(self, export_id: UUID, user_id: UUID) -> Optional[Dict[str, Any]]:
        """获取导出任务状态"""
        try:
            task_info = self.export_tasks.get(export_id)
            
            if not task_info:
                return None
            
            # 验证用户权限
            if task_info["user_id"] != user_id:
                return None
            
            return {
                "export_id": str(export_id),
                "status": task_info["status"],
                "progress": task_info["progress"],
                "created_at": task_info["created_at"].isoformat(),
                "started_at": task_info["started_at"].isoformat() if task_info["started_at"] else None,
                "completed_at": task_info["completed_at"].isoformat() if task_info["completed_at"] else None,
                "file_size": task_info["file_size"],
                "error_message": task_info["error_message"]
            }
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_STATUS_ERROR",
                message="获取导出状态失败",
                details={"error": str(e)}
            )
    
    async def download_export_file(
        self, 
        export_id: UUID, 
        user_id: UUID
    ) -> Tuple[BinaryIO, str, str]:
        """下载导出文件"""
        try:
            task_info = self.export_tasks.get(export_id)
            
            if not task_info:
                raise FileNotFoundError("导出任务不存在")
            
            # 验证用户权限
            if task_info["user_id"] != user_id:
                raise PermissionError("无权限访问此文件")
            
            # 检查任务状态
            if task_info["status"] != "completed":
                raise FileNotFoundError("导出文件尚未准备好")
            
            file_path = task_info["file_path"]
            if not file_path:
                raise FileNotFoundError("导出文件不存在")
            
            # 读取文件
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 确定文件名和媒体类型
            request_data = task_info["request"]
            export_format = request_data["format"]
            
            filename = f"usdt_data_{export_id}.{export_format}"
            
            media_type_map = {
                "csv": "text/csv",
                "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "json": "application/json"
            }
            
            media_type = media_type_map.get(export_format, "application/octet-stream")
            
            return io.BytesIO(file_content), filename, media_type
            
        except (FileNotFoundError, PermissionError):
            raise
        except Exception as e:
            raise AppException(
                error_code="EXPORT_DOWNLOAD_ERROR",
                message="下载导出文件失败",
                details={"error": str(e)}
            )
    
    def _prepare_export_data(
        self, 
        data_models: List, 
        export_request: USDTExportRequest
    ) -> List[Dict[str, Any]]:
        """准备导出数据"""
        try:
            export_data = []
            
            # 确定要导出的字段
            if export_request.fields:
                fields = export_request.fields
            else:
                # 默认字段
                fields = [
                    'timestamp', 'current_price_usd', 'market_cap_usd', 
                    'volume_24h', 'price_change_24h', 'price_change_percentage_24h', 
                    'data_source'
                ]
            
            for model in data_models:
                row_data = {}
                
                for field in fields:
                    if hasattr(model, field):
                        value = getattr(model, field)
                        
                        # 处理特殊类型
                        if isinstance(value, datetime):
                            row_data[field] = value.isoformat()
                        elif hasattr(value, '__float__'):  # Decimal类型
                            row_data[field] = float(value)
                        else:
                            row_data[field] = value
                    else:
                        row_data[field] = None
                
                export_data.append(row_data)
            
            return export_data
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_DATA_PREPARE_ERROR",
                message="准备导出数据失败",
                details={"error": str(e)}
            )
    
    async def _generate_export_file(
        self, 
        export_data: List[Dict[str, Any]], 
        export_request: USDTExportRequest,
        export_id: UUID
    ) -> Tuple[str, int]:
        """生成导出文件"""
        try:
            # 创建导出目录
            import os
            export_dir = os.path.join(settings.EXPORT_DIR, "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件路径
            file_extension = export_request.format
            file_path = os.path.join(export_dir, f"export_{export_id}.{file_extension}")
            
            # 根据格式生成文件
            if export_request.format == "csv":
                await self._generate_csv_file(export_data, file_path, export_request)
            elif export_request.format == "xlsx":
                await self._generate_xlsx_file(export_data, file_path, export_request)
            elif export_request.format == "json":
                await self._generate_json_file(export_data, file_path, export_request)
            else:
                raise AppException(
                    error_code="UNSUPPORTED_FORMAT",
                    message=f"不支持的导出格式: {export_request.format}"
                )
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            return file_path, file_size
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_FILE_GENERATE_ERROR",
                message="生成导出文件失败",
                details={"error": str(e)}
            )
    
    async def _generate_csv_file(
        self, 
        export_data: List[Dict[str, Any]], 
        file_path: str,
        export_request: USDTExportRequest
    ) -> None:
        """生成CSV文件"""
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            if export_data:
                fieldnames = list(export_data[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                writer.writerows(export_data)
            
            # 添加元数据（如果需要）
            if export_request.include_metadata:
                csvfile.write(f"\n# 导出时间: {datetime.utcnow().isoformat()}\n")
                csvfile.write(f"# 数据范围: {export_request.start_date} - {export_request.end_date}\n")
                csvfile.write(f"# 数据间隔: {export_request.interval}\n")
                csvfile.write(f"# 总记录数: {len(export_data)}\n")
    
    async def _generate_xlsx_file(
        self, 
        export_data: List[Dict[str, Any]], 
        file_path: str,
        export_request: USDTExportRequest
    ) -> None:
        """生成Excel文件"""
        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        # 使用ExcelWriter添加多个工作表
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 主数据工作表
            df.to_excel(writer, sheet_name='USDT数据', index=False)
            
            # 元数据工作表（如果需要）
            if export_request.include_metadata:
                metadata = {
                    '项目': ['导出时间', '开始时间', '结束时间', '数据间隔', '记录数量'],
                    '值': [
                        datetime.utcnow().isoformat(),
                        export_request.start_date.isoformat(),
                        export_request.end_date.isoformat(),
                        export_request.interval,
                        len(export_data)
                    ]
                }
                metadata_df = pd.DataFrame(metadata)
                metadata_df.to_excel(writer, sheet_name='元数据', index=False)
    
    async def _generate_json_file(
        self, 
        export_data: List[Dict[str, Any]], 
        file_path: str,
        export_request: USDTExportRequest
    ) -> None:
        """生成JSON文件"""
        output_data = {
            "data": export_data
        }
        
        # 添加元数据（如果需要）
        if export_request.include_metadata:
            output_data["metadata"] = {
                "export_time": datetime.utcnow().isoformat(),
                "start_date": export_request.start_date.isoformat(),
                "end_date": export_request.end_date.isoformat(),
                "interval": export_request.interval,
                "total_records": len(export_data),
                "format": export_request.format
            }
        
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(output_data, jsonfile, ensure_ascii=False, indent=2)
    
    def _parse_interval(self, interval: str) -> int:
        """解析时间间隔为分钟数"""
        interval_map = {
            "1m": 1,
            "5m": 5,
            "15m": 15,
            "1h": 60,
            "4h": 240,
            "1d": 1440
        }
        
        return interval_map.get(interval, 60)  # 默认1小时
    
    async def cleanup_old_exports(self, days: int = 7) -> int:
        """清理旧的导出文件"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            cleaned_count = 0
            
            # 清理内存中的任务记录
            expired_tasks = []
            for export_id, task_info in self.export_tasks.items():
                if task_info["created_at"] < cutoff_time:
                    expired_tasks.append(export_id)
            
            for export_id in expired_tasks:
                task_info = self.export_tasks.pop(export_id)
                
                # 删除文件
                if task_info.get("file_path"):
                    try:
                        import os
                        os.remove(task_info["file_path"])
                        cleaned_count += 1
                    except FileNotFoundError:
                        pass
            
            return cleaned_count
            
        except Exception as e:
            raise AppException(
                error_code="EXPORT_CLEANUP_ERROR",
                message="清理导出文件失败",
                details={"error": str(e)}
            )
