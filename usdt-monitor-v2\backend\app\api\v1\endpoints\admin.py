"""
管理员专用API端点
"""
from datetime import datetime, timedelta
from typing import Any, Dict, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.api.deps import get_current_admin_user
from app.domain.schemas.common import ApiResponse, PaginatedResponse, PaginationParams
from app.domain.schemas.user import User
from app.application.services.admin_service import AdminService

router = APIRouter()


@router.get("/dashboard", response_model=ApiResponse[Dict[str, Any]])
async def get_admin_dashboard(
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取管理员仪表板数据
    """
    try:
        dashboard_data = await admin_service.get_dashboard_data()
        
        return ApiResponse(
            data=dashboard_data,
            message="获取仪表板数据成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取仪表板数据失败"
        )


@router.get("/users/stats", response_model=ApiResponse[Dict[str, Any]])
async def get_user_stats(
    period: str = Query("30d", description="统计周期"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取用户统计数据
    """
    try:
        user_stats = await admin_service.get_user_stats(period)
        
        return ApiResponse(
            data=user_stats,
            message="获取用户统计成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计失败"
        )


@router.get("/activities", response_model=ApiResponse[PaginatedResponse[Dict[str, Any]]])
async def get_user_activities(
    pagination: PaginationParams = Depends(),
    user_id: UUID = Query(None, description="用户ID"),
    activity_type: str = Query(None, description="活动类型"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取用户活动日志
    """
    try:
        activities, total = await admin_service.get_user_activities(
            user_id=user_id,
            activity_type=activity_type,
            start_date=start_date,
            end_date=end_date,
            offset=pagination.offset,
            limit=pagination.page_size
        )
        
        paginated_response = PaginatedResponse.create(
            items=activities,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        return ApiResponse(
            data=paginated_response,
            message="获取活动日志成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取活动日志失败"
        )


@router.get("/alerts/stats", response_model=ApiResponse[Dict[str, Any]])
async def get_alert_stats(
    period: str = Query("7d", description="统计周期"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取告警统计数据
    """
    try:
        alert_stats = await admin_service.get_alert_stats(period)
        
        return ApiResponse(
            data=alert_stats,
            message="获取告警统计成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取告警统计失败"
        )


@router.get("/data/quality", response_model=ApiResponse[Dict[str, Any]])
async def get_data_quality_report(
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取数据质量报告
    """
    try:
        quality_report = await admin_service.get_data_quality_report()
        
        return ApiResponse(
            data=quality_report,
            message="获取数据质量报告成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取数据质量报告失败"
        )


@router.post("/data/cleanup", response_model=ApiResponse[Dict[str, Any]])
async def cleanup_old_data(
    days: int = Query(90, description="保留天数"),
    dry_run: bool = Query(True, description="是否为试运行"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    清理旧数据
    """
    try:
        if days < 7:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="保留天数不能少于7天"
            )
        
        cleanup_result = await admin_service.cleanup_old_data(days, dry_run)
        
        return ApiResponse(
            data=cleanup_result,
            message="数据清理完成" if not dry_run else "数据清理预览完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="数据清理失败"
        )


@router.get("/security/audit", response_model=ApiResponse[Dict[str, Any]])
async def get_security_audit(
    period: str = Query("7d", description="审计周期"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取安全审计报告
    """
    try:
        audit_report = await admin_service.get_security_audit(period)
        
        return ApiResponse(
            data=audit_report,
            message="获取安全审计报告成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取安全审计报告失败"
        )


@router.get("/performance/metrics", response_model=ApiResponse[Dict[str, Any]])
async def get_performance_metrics(
    period: str = Query("24h", description="统计周期"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取性能指标
    """
    try:
        metrics = await admin_service.get_performance_metrics(period)
        
        return ApiResponse(
            data=metrics,
            message="获取性能指标成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能指标失败"
        )


@router.post("/notifications/broadcast", response_model=ApiResponse[Dict[str, Any]])
async def broadcast_notification(
    notification_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    发送广播通知
    """
    try:
        # 验证通知数据
        if not notification_data.get("title") or not notification_data.get("message"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="通知标题和内容不能为空"
            )
        
        result = await admin_service.broadcast_notification(notification_data)
        
        return ApiResponse(
            data=result,
            message="广播通知发送成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送广播通知失败"
        )


@router.get("/exports", response_model=ApiResponse[PaginatedResponse[Dict[str, Any]]])
async def get_export_tasks(
    pagination: PaginationParams = Depends(),
    status_filter: str = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    获取导出任务列表
    """
    try:
        exports, total = await admin_service.get_export_tasks(
            status_filter=status_filter,
            offset=pagination.offset,
            limit=pagination.page_size
        )
        
        paginated_response = PaginatedResponse.create(
            items=exports,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        return ApiResponse(
            data=paginated_response,
            message="获取导出任务列表成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取导出任务列表失败"
        )


@router.delete("/exports/{export_id}", response_model=ApiResponse[dict])
async def delete_export_task(
    export_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    删除导出任务
    """
    try:
        success = await admin_service.delete_export_task(export_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="导出任务不存在"
            )
        
        return ApiResponse(
            data={"message": "导出任务删除成功"},
            message="导出任务删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除导出任务失败"
        )


@router.post("/maintenance/schedule", response_model=ApiResponse[Dict[str, Any]])
async def schedule_maintenance(
    maintenance_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends()
) -> Any:
    """
    安排系统维护
    """
    try:
        # 验证维护数据
        required_fields = ["start_time", "end_time", "description"]
        for field in required_fields:
            if field not in maintenance_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少必需字段: {field}"
                )
        
        result = await admin_service.schedule_maintenance(maintenance_data)
        
        return ApiResponse(
            data=result,
            message="维护计划创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建维护计划失败"
        )
