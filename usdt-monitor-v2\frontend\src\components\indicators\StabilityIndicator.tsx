import React from 'react'
import { Progress, Tag, Tooltip } from 'antd'
import { CheckCircleOutlined, WarningOutlined, CloseCircleOutlined } from '@ant-design/icons'

interface StabilityIndicatorProps {
  score: number
  size?: 'small' | 'default' | 'large'
  showText?: boolean
  className?: string
}

export const StabilityIndicator: React.FC<StabilityIndicatorProps> = ({
  score,
  size = 'small',
  showText = true,
  className
}) => {
  // 获取稳定性等级
  const getStabilityLevel = (score: number) => {
    if (score >= 0.8) return 'excellent'
    if (score >= 0.6) return 'good'
    if (score >= 0.4) return 'fair'
    return 'poor'
  }

  // 获取稳定性配置
  const getStabilityConfig = (level: string) => {
    switch (level) {
      case 'excellent':
        return {
          color: '#52c41a',
          text: '优秀',
          icon: <CheckCircleOutlined />,
          description: '价格非常稳定，偏离锚定价格很小'
        }
      case 'good':
        return {
          color: '#1890ff',
          text: '良好',
          icon: <CheckCircleOutlined />,
          description: '价格相对稳定，偏离锚定价格较小'
        }
      case 'fair':
        return {
          color: '#faad14',
          text: '一般',
          icon: <WarningOutlined />,
          description: '价格波动较大，需要关注'
        }
      case 'poor':
        return {
          color: '#ff4d4f',
          text: '较差',
          icon: <CloseCircleOutlined />,
          description: '价格波动很大，存在风险'
        }
      default:
        return {
          color: '#d9d9d9',
          text: '未知',
          icon: <WarningOutlined />,
          description: '无法评估稳定性'
        }
    }
  }

  const level = getStabilityLevel(score)
  const config = getStabilityConfig(level)
  const percentage = Math.round(score * 100)

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* 进度条 */}
      <div className="flex-1">
        <Progress
          percent={percentage}
          size={size}
          strokeColor={config.color}
          showInfo={false}
        />
      </div>

      {/* 稳定性标签 */}
      {showText && (
        <Tooltip title={config.description}>
          <Tag
            color={config.color}
            icon={config.icon}
            className="cursor-help"
          >
            {config.text}
          </Tag>
        </Tooltip>
      )}
    </div>
  )
}
