"""
API v1模块
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, usdt, system, admin

api_router = APIRouter()

# 包含各个端点路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(usdt.router, prefix="/usdt", tags=["USDT数据"])
api_router.include_router(system.router, prefix="/system", tags=["系统管理"])
api_router.include_router(admin.router, prefix="/admin", tags=["管理员"])
