{"version": 3, "sources": ["../../dayjs/plugin/utc.js"], "sourcesContent": ["!function(t,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs_plugin_utc=i()}(this,(function(){\"use strict\";var t=\"minute\",i=/[+-]\\d\\d(?::?\\d\\d)?/g,e=/([+-]|\\d\\d)/g;return function(s,f,n){var u=f.prototype;n.utc=function(t){var i={date:t,utc:!0,args:arguments};return new f(i)},u.utc=function(i){var e=n(this.toDate(),{locale:this.$L,utc:!0});return i?e.add(this.utcOffset(),t):e},u.local=function(){return n(this.toDate(),{locale:this.$L,utc:!1})};var o=u.parse;u.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),o.call(this,t)};var r=u.init;u.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else r.call(this)};var a=u.utcOffset;u.utcOffset=function(s,f){var n=this.$utils().u;if(n(s))return this.$u?0:n(this.$offset)?a.call(this):this.$offset;if(\"string\"==typeof s&&(s=function(t){void 0===t&&(t=\"\");var s=t.match(i);if(!s)return null;var f=(\"\"+s[0]).match(e)||[\"-\",0,0],n=f[0],u=60*+f[1]+ +f[2];return 0===u?0:\"+\"===n?u:-u}(s),null===s))return this;var u=Math.abs(s)<=16?60*s:s,o=this;if(f)return o.$offset=u,o.$u=0===s,o;if(0!==s){var r=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(o=this.local().add(u+r,t)).$offset=u,o.$x.$localOffset=r}else o=this.utc();return o};var h=u.format;u.format=function(t){var i=t||(this.$u?\"YYYY-MM-DDTHH:mm:ss[Z]\":\"\");return h.call(this,i)},u.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*t},u.isUTC=function(){return!!this.$u},u.toISOString=function(){return this.toDate().toISOString()},u.toString=function(){return this.toDate().toUTCString()};var l=u.toDate;u.toDate=function(t){return\"s\"===t&&this.$offset?n(this.format(\"YYYY-MM-DD HH:mm:ss:SSS\")).toDate():l.call(this)};var c=u.diff;u.diff=function(t,i,e){if(t&&this.$u===t.$u)return c.call(this,t,i,e);var s=this.local(),f=n(t).local();return c.call(s,f,i,e)}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,mBAAiB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAI,IAAE,UAAS,IAAE,wBAAuB,IAAE;AAAe,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE;AAAU,UAAE,MAAI,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAC,MAAKD,IAAE,KAAI,MAAG,MAAK,UAAS;AAAE,iBAAO,IAAI,EAAEC,EAAC;AAAA,QAAC,GAAE,EAAE,MAAI,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAE,KAAK,OAAO,GAAE,EAAC,QAAO,KAAK,IAAG,KAAI,KAAE,CAAC;AAAE,iBAAOD,KAAEC,GAAE,IAAI,KAAK,UAAU,GAAE,CAAC,IAAEA;AAAA,QAAC,GAAE,EAAE,QAAM,WAAU;AAAC,iBAAO,EAAE,KAAK,OAAO,GAAE,EAAC,QAAO,KAAK,IAAG,KAAI,MAAE,CAAC;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAM,UAAE,QAAM,SAASF,IAAE;AAAC,UAAAA,GAAE,QAAM,KAAK,KAAG,OAAI,KAAK,OAAO,EAAE,EAAEA,GAAE,OAAO,MAAI,KAAK,UAAQA,GAAE,UAAS,EAAE,KAAK,MAAKA,EAAC;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAK,UAAE,OAAK,WAAU;AAAC,cAAG,KAAK,IAAG;AAAC,gBAAIA,KAAE,KAAK;AAAG,iBAAK,KAAGA,GAAE,eAAe,GAAE,KAAK,KAAGA,GAAE,YAAY,GAAE,KAAK,KAAGA,GAAE,WAAW,GAAE,KAAK,KAAGA,GAAE,UAAU,GAAE,KAAK,KAAGA,GAAE,YAAY,GAAE,KAAK,KAAGA,GAAE,cAAc,GAAE,KAAK,KAAGA,GAAE,cAAc,GAAE,KAAK,MAAIA,GAAE,mBAAmB;AAAA,UAAC,MAAM,GAAE,KAAK,IAAI;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAU,UAAE,YAAU,SAASG,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,OAAO,EAAE;AAAE,cAAGA,GAAEF,EAAC,EAAE,QAAO,KAAK,KAAG,IAAEE,GAAE,KAAK,OAAO,IAAE,EAAE,KAAK,IAAI,IAAE,KAAK;AAAQ,cAAG,YAAU,OAAOF,OAAIA,KAAE,SAASH,IAAE;AAAC,uBAASA,OAAIA,KAAE;AAAI,gBAAIG,KAAEH,GAAE,MAAM,CAAC;AAAE,gBAAG,CAACG,GAAE,QAAO;AAAK,gBAAIC,MAAG,KAAGD,GAAE,CAAC,GAAG,MAAM,CAAC,KAAG,CAAC,KAAI,GAAE,CAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAE,KAAG,CAACF,GAAE,CAAC,IAAG,CAACA,GAAE,CAAC;AAAE,mBAAO,MAAIE,KAAE,IAAE,QAAMD,KAAEC,KAAE,CAACA;AAAA,UAAC,EAAEH,EAAC,GAAE,SAAOA,IAAG,QAAO;AAAK,cAAIG,KAAE,KAAK,IAAIH,EAAC,KAAG,KAAG,KAAGA,KAAEA,IAAEI,KAAE;AAAK,cAAGH,GAAE,QAAOG,GAAE,UAAQD,IAAEC,GAAE,KAAG,MAAIJ,IAAEI;AAAE,cAAG,MAAIJ,IAAE;AAAC,gBAAIK,KAAE,KAAK,KAAG,KAAK,OAAO,EAAE,kBAAkB,IAAE,KAAG,KAAK,UAAU;AAAE,aAACD,KAAE,KAAK,MAAM,EAAE,IAAID,KAAEE,IAAE,CAAC,GAAG,UAAQF,IAAEC,GAAE,GAAG,eAAaC;AAAA,UAAC,MAAM,CAAAD,KAAE,KAAK,IAAI;AAAE,iBAAOA;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAO,UAAE,SAAO,SAASP,IAAE;AAAC,cAAIC,KAAED,OAAI,KAAK,KAAG,2BAAyB;AAAI,iBAAO,EAAE,KAAK,MAAKC,EAAC;AAAA,QAAC,GAAE,EAAE,UAAQ,WAAU;AAAC,cAAID,KAAE,KAAK,OAAO,EAAE,EAAE,KAAK,OAAO,IAAE,IAAE,KAAK,WAAS,KAAK,GAAG,gBAAc,KAAK,GAAG,kBAAkB;AAAG,iBAAO,KAAK,GAAG,QAAQ,IAAE,MAAIA;AAAA,QAAC,GAAE,EAAE,QAAM,WAAU;AAAC,iBAAM,CAAC,CAAC,KAAK;AAAA,QAAE,GAAE,EAAE,cAAY,WAAU;AAAC,iBAAO,KAAK,OAAO,EAAE,YAAY;AAAA,QAAC,GAAE,EAAE,WAAS,WAAU;AAAC,iBAAO,KAAK,OAAO,EAAE,YAAY;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAO,UAAE,SAAO,SAASA,IAAE;AAAC,iBAAM,QAAMA,MAAG,KAAK,UAAQ,EAAE,KAAK,OAAO,yBAAyB,CAAC,EAAE,OAAO,IAAE,EAAE,KAAK,IAAI;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAK,UAAE,OAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAGF,MAAG,KAAK,OAAKA,GAAE,GAAG,QAAO,EAAE,KAAK,MAAKA,IAAEC,IAAEC,EAAC;AAAE,cAAIC,KAAE,KAAK,MAAM,GAAEC,KAAE,EAAEJ,EAAC,EAAE,MAAM;AAAE,iBAAO,EAAE,KAAKG,IAAEC,IAAEH,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["t", "i", "e", "s", "f", "n", "u", "o", "r"]}