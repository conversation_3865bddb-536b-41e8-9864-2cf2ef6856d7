import React from 'react'
import { <PERSON>, Typography, Badge, Button } from 'antd'
import { BellOutlined, WarningOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { usdtApi } from '../../services/api/usdt'

const { Text } = Typography

export const AlertSummary: React.FC = () => {
  // 获取用户告警列表
  const { data: alertsData, isLoading } = useQuery({
    queryKey: ['usdt', 'alerts', 'summary'],
    queryFn: () => usdtApi.getUserAlerts({ page: 1, size: 10, is_active: true }),
    refetchInterval: 60000, // 1分钟刷新
  })

  const alerts = alertsData?.data?.items || []
  const activeAlerts = alerts.filter(alert => alert.is_active)
  const triggeredAlerts = alerts.filter(alert => alert.triggered_count > 0)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-20">
        <Text type="secondary">加载中...</Text>
      </div>
    )
  }

  return (
    <Space direction="vertical" className="w-full">
      {/* 告警统计 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Text type="secondary">活跃告警:</Text>
          <Badge count={activeAlerts.length} showZero>
            <BellOutlined className="text-blue-500" />
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <Text type="secondary">已触发:</Text>
          <Badge count={triggeredAlerts.length} showZero>
            <WarningOutlined className="text-orange-500" />
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <Text type="secondary">总计:</Text>
          <Badge count={alerts.length} showZero>
            <CheckCircleOutlined className="text-green-500" />
          </Badge>
        </div>
      </div>

      {/* 最近告警 */}
      {triggeredAlerts.length > 0 && (
        <div className="mt-4">
          <Text type="secondary" className="text-xs">
            最近触发:
          </Text>
          <div className="mt-1 space-y-1">
            {triggeredAlerts.slice(0, 3).map(alert => (
              <div key={alert.id} className="text-xs">
                <Text type="secondary">
                  {alert.alert_type === 'price_above' && '价格超过'}
                  {alert.alert_type === 'price_below' && '价格低于'}
                  {alert.alert_type === 'volatility_high' && '波动率过高'}
                  {alert.alert_type === 'deviation_high' && '偏离过大'}
                  {' '}${alert.threshold_value.toFixed(4)}
                </Text>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 快速操作 */}
      <div className="mt-4">
        <Button type="link" size="small" className="p-0">
          管理告警
        </Button>
      </div>
    </Space>
  )
}
