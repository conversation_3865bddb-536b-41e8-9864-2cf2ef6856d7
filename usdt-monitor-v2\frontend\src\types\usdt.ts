// USDT数据类型
export interface USDTData {
  id: string
  timestamp: string
  unix_timestamp: number
  name: string
  symbol: string
  current_price_usd: number
  price_precision?: string
  data_source: string
  source_id?: string
  last_updated?: string
  
  // 市场数据
  market_cap_usd?: number
  market_cap_rank?: number
  total_volume_usd?: number
  circulating_supply?: number
  total_supply?: number
  max_supply?: number
  
  // 价格变化数据
  price_change_24h?: number
  price_change_percentage_24h?: number
  price_change_7d?: number
  price_change_percentage_7d?: number
  price_change_30d?: number
  price_change_percentage_30d?: number
  
  // 市值变化数据
  market_cap_change_24h?: number
  market_cap_change_percentage_24h?: number
  
  // 交易量数据
  volume_24h?: number
  volume_change_24h?: number
  
  // 技术指标
  ath?: number
  ath_date?: string
  atl?: number
  atl_date?: string
  
  // 数据质量
  confidence_score?: number
  is_anomaly: boolean
  created_at: string
}

// USDT统计数据
export interface USDTStats {
  period: string
  current_price: number
  avg_price: number
  min_price: number
  max_price: number
  price_range: number
  volatility: number
  total_data_points: number
  
  // 价格变化统计
  price_change?: number
  price_change_percentage?: number
  
  // 交易量统计
  avg_volume?: number
  total_volume?: number
  
  // 稳定性指标
  stability_score: number
  deviation_from_peg: number
  
  // 时间范围
  start_time: string
  end_time: string
}

// USDT告警类型
export interface USDTAlert {
  id: string
  user_id: string
  alert_type: 'price_above' | 'price_below' | 'volatility_high' | 'deviation_high'
  threshold_value: number
  is_active: boolean
  description?: string
  triggered_count: number
  last_triggered?: string
  created_at: string
  updated_at?: string
}

// 创建告警请求
export interface USDTAlertCreate {
  alert_type: 'price_above' | 'price_below' | 'volatility_high' | 'deviation_high'
  threshold_value: number
  is_active?: boolean
  description?: string
}

// 历史数据查询
export interface USDTHistoryQuery {
  start_date: string
  end_date: string
  interval?: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  source?: string
}

// 数据导出请求
export interface USDTExportRequest {
  start_date: string
  end_date: string
  format: 'csv' | 'xlsx' | 'json'
  interval?: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  fields?: string[]
  include_metadata?: boolean
}

// 图表数据点
export interface USDTChartData {
  timestamp: string
  price: number
  volume?: number
  market_cap?: number
}

// 价格告警通知
export interface PriceAlert {
  alert_id: string
  user_id: string
  alert_type: string
  threshold_value: number
  current_value: number
  message: string
  triggered_at: string
}

// 市场摘要
export interface MarketSummary {
  current_price: number
  price_change_24h?: number
  price_change_percentage_24h?: number
  market_cap?: number
  volume_24h?: number
  last_updated: string
  
  // 稳定性指标
  is_stable: boolean
  deviation_from_peg: number
  stability_score: number
  
  // 趋势指标
  trend_direction: 'up' | 'down' | 'stable'
  trend_strength: number
}

// 数据质量报告
export interface DataQualityReport {
  period: string
  total_data_points: number
  valid_data_points: number
  anomaly_count: number
  missing_data_count: number
  
  // 质量指标
  completeness_score: number
  accuracy_score: number
  consistency_score: number
  overall_quality_score: number
  
  // 数据源统计
  source_distribution: Record<string, number>
  
  // 时间范围
  start_time: string
  end_time: string
  generated_at: string
}

// 实时数据订阅选项
export interface RealtimeSubscriptionOptions {
  interval?: number // 轮询间隔（毫秒）
  onData?: (data: USDTData) => void
  onError?: (error: Error) => void
  onConnect?: () => void
  onDisconnect?: () => void
}

// 图表配置
export interface ChartConfig {
  type: 'line' | 'candlestick' | 'area'
  period: '1h' | '24h' | '7d' | '30d'
  interval: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  indicators?: ('ma' | 'ema' | 'bollinger' | 'rsi')[]
  theme?: 'light' | 'dark'
}

// 导出任务状态
export interface ExportTask {
  export_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  format?: string
  created_at: string
  started_at?: string
  completed_at?: string
  file_size?: number
  error_message?: string
  download_url?: string
}
