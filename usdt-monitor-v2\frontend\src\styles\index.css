@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 深色模式 */
.dark {
  color-scheme: dark;
}

/* Ant Design 自定义样式 */
.ant-layout {
  background: transparent;
  min-height: 100vh;
}

.ant-layout-content {
  padding: 16px;
  overflow: auto;
  flex: 1;
}

.ant-layout-header {
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu {
  border-right: none;
}

.ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
}

.ant-menu-submenu {
  margin: 4px 8px;
}

.ant-menu-submenu > .ant-menu-submenu-title {
  border-radius: 6px;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.dark .ant-card-head {
  border-bottom-color: #303030;
}

/* 表格样式 */
.ant-table {
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.dark .ant-table-thead > tr > th {
  background: #1f1f1f;
  border-bottom-color: #303030;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 24px;
}

.ant-input,
.ant-input-password {
  border-radius: 6px;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
}

/* 统计数字样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
}

.dark .ant-statistic-title {
  color: rgba(255, 255, 255, 0.45);
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 进度条样式 */
.ant-progress-line {
  margin-right: 8px;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 7px;
}

/* 徽章样式 */
.ant-badge {
  font-size: 12px;
}

/* 工具提示样式 */
.ant-tooltip {
  font-size: 12px;
}

/* 下拉菜单样式 */
.ant-dropdown {
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* 模态框样式 */
.ant-modal {
  border-radius: 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #f0f0f0;
}

.dark .ant-modal-header {
  border-bottom-color: #303030;
}

/* 抽屉样式 */
.ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
}

.dark .ant-drawer-header {
  border-bottom-color: #303030;
}

/* 分页样式 */
.ant-pagination {
  text-align: center;
  margin-top: 24px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

/* 加载样式 */
.ant-spin {
  color: #1890ff;
}

/* 自定义工具类 */
.text-primary {
  color: #1890ff;
}

.text-primary-dark {
  color: #096dd9;
}

.bg-primary {
  background-color: #1890ff;
}

.border-primary {
  border-color: #1890ff;
}

/* 响应式容器 */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400px;
  min-height: 300px;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 表格响应式 */
.ant-table-wrapper {
  overflow-x: auto;
}

/* 响应式工具类 */
@media (max-width: 1200px) {
  .responsive-container {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .responsive-container {
    padding: 0 12px;
  }

  .ant-layout-header {
    padding: 0 16px;
  }

  .ant-layout-content {
    padding: 12px;
  }

  .ant-card {
    margin: 8px 0;
  }

  .ant-statistic-content {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .chart-container {
    height: 300px;
    min-height: 250px;
  }

  .button-group {
    flex-direction: column;
  }

  .button-group .ant-btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .responsive-container {
    padding: 0 8px;
  }

  .ant-layout-content {
    padding: 8px;
  }

  .ant-card-body {
    padding: 16px;
  }

  .stats-grid {
    gap: 8px;
  }

  .chart-container {
    height: 250px;
    min-height: 200px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark ::-webkit-scrollbar-track {
  background: #2f2f2f;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 打印样式 */
@media print {
  .ant-layout-sider,
  .ant-layout-header,
  .no-print {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}
