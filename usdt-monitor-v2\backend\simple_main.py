"""
简化的USDT监控平台后端 - 用于快速启动和测试
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import time
import random
from datetime import datetime, timedelta

# 创建FastAPI应用
app = FastAPI(
    title="USDT Monitor Platform V2",
    version="2.0.0",
    description="专业的USDT价格监控平台",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class UserLogin(BaseModel):
    email: str
    password: str

class UserRegister(BaseModel):
    email: str
    password: str
    full_name: str

class User(BaseModel):
    id: int
    email: str
    full_name: str
    is_active: bool
    is_admin: bool
    created_at: datetime

class Token(BaseModel):
    access_token: str
    token_type: str
    user: User

class USDTPrice(BaseModel):
    id: int
    price: float
    source: str
    timestamp: datetime
    volume_24h: Optional[float] = None
    market_cap: Optional[float] = None

class PriceStats(BaseModel):
    current_price: float
    price_24h_ago: float
    change_24h: float
    change_24h_percent: float
    high_24h: float
    low_24h: float
    volume_24h: float

# 模拟数据
mock_user = User(
    id=1,
    email="<EMAIL>",
    full_name="管理员",
    is_active=True,
    is_admin=True,
    created_at=datetime.now()
)

# 生成模拟USDT价格数据
def generate_mock_price():
    base_price = 1.0
    variation = random.uniform(-0.01, 0.01)
    return round(base_price + variation, 4)

def generate_mock_history(hours: int = 24):
    prices = []
    now = datetime.now()
    for i in range(hours):
        timestamp = now - timedelta(hours=hours-i)
        price = generate_mock_price()
        prices.append(USDTPrice(
            id=i+1,
            price=price,
            source="mock",
            timestamp=timestamp,
            volume_24h=random.uniform(1000000, 5000000),
            market_cap=random.uniform(80000000000, 90000000000)
        ))
    return prices

# API路由

@app.get("/")
async def root():
    return {
        "message": "USDT Monitor Platform V2 API",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": time.time()
    }

# 认证相关API
@app.post("/api/v1/auth/login", response_model=Token)
async def login(user_data: UserLogin):
    # 简单的模拟登录
    if user_data.email == "<EMAIL>" and user_data.password == "admin123":
        return Token(
            access_token="mock-jwt-token",
            token_type="bearer",
            user=mock_user
        )
    raise HTTPException(status_code=401, detail="Invalid credentials")

@app.post("/api/v1/auth/register", response_model=Token)
async def register(user_data: UserRegister):
    # 简单的模拟注册
    new_user = User(
        id=2,
        email=user_data.email,
        full_name=user_data.full_name,
        is_active=True,
        is_admin=False,
        created_at=datetime.now()
    )
    return Token(
        access_token="mock-jwt-token",
        token_type="bearer",
        user=new_user
    )

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user():
    return mock_user

# USDT价格相关API
@app.get("/api/v1/usdt/current", response_model=USDTPrice)
async def get_current_price():
    return USDTPrice(
        id=1,
        price=generate_mock_price(),
        source="mock",
        timestamp=datetime.now(),
        volume_24h=random.uniform(1000000, 5000000),
        market_cap=random.uniform(80000000000, 90000000000)
    )

@app.get("/api/v1/usdt/history", response_model=List[USDTPrice])
async def get_price_history(hours: int = 24):
    return generate_mock_history(hours)

@app.get("/api/v1/usdt/stats", response_model=PriceStats)
async def get_price_stats():
    current = generate_mock_price()
    price_24h_ago = generate_mock_price()
    change = current - price_24h_ago
    change_percent = (change / price_24h_ago) * 100 if price_24h_ago != 0 else 0
    
    return PriceStats(
        current_price=current,
        price_24h_ago=price_24h_ago,
        change_24h=change,
        change_24h_percent=round(change_percent, 2),
        high_24h=current + random.uniform(0, 0.02),
        low_24h=current - random.uniform(0, 0.02),
        volume_24h=random.uniform(1000000, 5000000)
    )

# 图表数据API
@app.get("/api/v1/usdt/chart")
async def get_chart_data(interval: str = "1h", limit: int = 24):
    data = []
    now = datetime.now()
    
    for i in range(limit):
        if interval == "1h":
            timestamp = now - timedelta(hours=limit-i)
        elif interval == "1d":
            timestamp = now - timedelta(days=limit-i)
        else:
            timestamp = now - timedelta(hours=limit-i)
            
        price = generate_mock_price()
        data.append({
            "timestamp": timestamp.isoformat(),
            "price": price,
            "volume": random.uniform(100000, 500000)
        })
    
    return {"data": data}

# 告警相关API
@app.get("/api/v1/alerts")
async def get_alerts():
    return {
        "alerts": [
            {
                "id": 1,
                "type": "price_above",
                "threshold": 1.01,
                "is_active": True,
                "created_at": datetime.now().isoformat()
            }
        ]
    }

@app.post("/api/v1/alerts")
async def create_alert(alert_data: dict):
    return {
        "id": random.randint(1, 1000),
        "message": "Alert created successfully",
        **alert_data
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
