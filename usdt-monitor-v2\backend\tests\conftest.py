"""
测试配置和fixtures
"""
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.config import settings
from app.core.database import Base, get_db
from app.core.security import create_access_token
from app.domain.models.user import User as UserModel
from app.domain.schemas.user import UserCreate


# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False
)

# 创建测试会话
TestingSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    # 创建所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建会话
    async with TestingSessionLocal() as session:
        yield session
    
    # 清理
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client(db_session: AsyncSession) -> TestClient:
    """创建测试客户端"""
    def override_get_db():
        return db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> UserModel:
    """创建测试用户"""
    from app.core.security import get_password_hash
    
    user = UserModel(
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password=get_password_hash("testpassword"),
        is_active=True,
        is_verified=True,
        is_superuser=False
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession) -> UserModel:
    """创建测试管理员用户"""
    from app.core.security import get_password_hash
    
    admin = UserModel(
        email="<EMAIL>",
        username="admin",
        full_name="Admin User",
        hashed_password=get_password_hash("adminpassword"),
        is_active=True,
        is_verified=True,
        is_superuser=True
    )
    
    db_session.add(admin)
    await db_session.commit()
    await db_session.refresh(admin)
    
    return admin


@pytest.fixture
def user_token(test_user: UserModel) -> str:
    """创建用户访问令牌"""
    return create_access_token(data={"sub": test_user.email})


@pytest.fixture
def admin_token(test_admin_user: UserModel) -> str:
    """创建管理员访问令牌"""
    return create_access_token(data={"sub": test_admin_user.email})


@pytest.fixture
def auth_headers(user_token: str) -> dict:
    """创建认证头"""
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def admin_headers(admin_token: str) -> dict:
    """创建管理员认证头"""
    return {"Authorization": f"Bearer {admin_token}"}


@pytest_asyncio.fixture
async def sample_usdt_data(db_session: AsyncSession) -> list:
    """创建示例USDT数据"""
    from datetime import datetime, timedelta
    from decimal import Decimal
    from app.domain.models.usdt_data import USDTData
    
    base_time = datetime.utcnow()
    sample_data = []
    
    for i in range(10):
        data = USDTData(
            timestamp=base_time - timedelta(hours=i),
            unix_timestamp=int((base_time - timedelta(hours=i)).timestamp()),
            name="Tether",
            symbol="USDT",
            current_price_usd=Decimal("1.0001") + Decimal(str(i * 0.0001)),
            price_precision=f"{1.0001 + i * 0.0001:.10f}",
            data_source="test",
            source_id="test_source",
            last_updated=base_time - timedelta(hours=i),
            market_cap_usd=80000000000 + i * 1000000,
            market_cap_rank=3,
            total_volume_usd=50000000000 + i * 1000000,
            circulating_supply=Decimal("80000000000"),
            total_supply=Decimal("80000000000"),
            confidence_score=0.9,
            is_anomaly=False
        )
        
        db_session.add(data)
        sample_data.append(data)
    
    await db_session.commit()
    
    for data in sample_data:
        await db_session.refresh(data)
    
    return sample_data


@pytest.fixture
def mock_coingecko_client():
    """模拟CoinGecko客户端"""
    mock_client = AsyncMock()
    
    # 模拟获取USDT数据
    mock_client.get_usdt_data.return_value = {
        "current_price_usd": 1.0001,
        "market_cap_usd": 80000000000,
        "volume_24h": 50000000000,
        "price_change_24h": 0.0001,
        "last_updated": "2024-01-01T00:00:00Z",
        "data_source": "coingecko",
        "timestamp": "2024-01-01T00:00:00Z",
        "unix_timestamp": **********
    }
    
    # 模拟健康检查
    mock_client.health_check.return_value = True
    
    return mock_client


@pytest.fixture
def mock_cache_manager():
    """模拟缓存管理器"""
    mock_cache = AsyncMock()
    
    # 模拟缓存操作
    mock_cache.get.return_value = None
    mock_cache.set.return_value = True
    mock_cache.delete.return_value = True
    mock_cache.clear.return_value = 0
    
    return mock_cache


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    mock_redis = AsyncMock()
    
    # 模拟Redis info
    mock_redis.info.return_value = {
        "keyspace_hits": 1000,
        "keyspace_misses": 100,
        "used_memory": 1024000,
        "connected_clients": 5,
        "db0": {"keys": 50}
    }
    
    # 模拟其他Redis操作
    mock_redis.keys.return_value = ["key1", "key2", "key3"]
    mock_redis.delete.return_value = 3
    
    return mock_redis


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "email": "<EMAIL>",
        "username": "newuser",
        "full_name": "New User",
        "password": "newpassword123"
    }


@pytest.fixture
def sample_usdt_export_request():
    """示例USDT导出请求"""
    from datetime import datetime, timedelta
    
    return {
        "start_date": (datetime.utcnow() - timedelta(days=1)).isoformat(),
        "end_date": datetime.utcnow().isoformat(),
        "format": "csv",
        "interval": "1h",
        "fields": ["timestamp", "current_price_usd", "volume_24h"],
        "include_metadata": True
    }


@pytest.fixture
def sample_alert_data():
    """示例告警数据"""
    return {
        "alert_type": "price_above",
        "threshold_value": "1.01",
        "is_active": True,
        "description": "USDT价格超过1.01美元时告警"
    }


class AsyncContextManager:
    """异步上下文管理器辅助类"""
    
    def __init__(self, async_obj):
        self.async_obj = async_obj
    
    async def __aenter__(self):
        return self.async_obj
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


@pytest.fixture
def async_context_manager():
    """异步上下文管理器工厂"""
    return AsyncContextManager


# 测试工具函数
def assert_response_success(response, expected_status=200):
    """断言响应成功"""
    assert response.status_code == expected_status
    data = response.json()
    assert data.get("success") is True
    return data


def assert_response_error(response, expected_status=400):
    """断言响应错误"""
    assert response.status_code == expected_status
    data = response.json()
    assert data.get("success") is False
    return data


def create_test_headers(token: str) -> dict:
    """创建测试请求头"""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }


# 测试数据生成器
class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_user_data(email_suffix: str = None) -> dict:
        """生成用户数据"""
        import uuid
        suffix = email_suffix or str(uuid.uuid4())[:8]
        
        return {
            "email": f"user_{suffix}@example.com",
            "username": f"user_{suffix}",
            "full_name": f"Test User {suffix}",
            "password": "testpassword123"
        }
    
    @staticmethod
    def generate_usdt_data(price: float = 1.0001) -> dict:
        """生成USDT数据"""
        from datetime import datetime
        
        return {
            "timestamp": datetime.utcnow(),
            "unix_timestamp": int(datetime.utcnow().timestamp()),
            "name": "Tether",
            "symbol": "USDT",
            "current_price_usd": price,
            "data_source": "test",
            "market_cap_usd": 80000000000,
            "volume_24h": 50000000000,
            "confidence_score": 0.9,
            "is_anomaly": False
        }


@pytest.fixture
def test_data_generator():
    """测试数据生成器fixture"""
    return TestDataGenerator
