"""
用户仓储实现
"""
from typing import List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.domain.models.user import User as UserModel, Role, Permission, UserActivity
from app.domain.schemas.user import UserCreate, UserUpdate
from app.infrastructure.repositories.base import BaseRepository


class UserRepository(BaseRepository[UserModel, UserCreate, UserUpdate]):
    """用户仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(UserModel, db)
    
    async def get_by_email(self, email: str) -> Optional[UserModel]:
        """根据邮箱获取用户"""
        result = await self.db.execute(
            select(UserModel)
            .options(selectinload(UserModel.roles).selectinload(Role.permissions))
            .where(UserModel.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_by_username(self, username: str) -> Optional[UserModel]:
        """根据用户名获取用户"""
        result = await self.db.execute(
            select(UserModel)
            .options(selectinload(UserModel.roles).selectinload(Role.permissions))
            .where(UserModel.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_with_roles(self, user_id: UUID) -> Optional[UserModel]:
        """获取用户及其角色信息"""
        result = await self.db.execute(
            select(UserModel)
            .options(selectinload(UserModel.roles).selectinload(Role.permissions))
            .where(UserModel.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def search_users(
        self,
        *,
        search_query: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_superuser: Optional[bool] = None,
        is_verified: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[UserModel], int]:
        """搜索用户"""
        # 构建查询条件
        conditions = []
        
        if search_query:
            search_conditions = [
                UserModel.email.ilike(f"%{search_query}%"),
                UserModel.username.ilike(f"%{search_query}%"),
                UserModel.full_name.ilike(f"%{search_query}%")
            ]
            conditions.append(or_(*search_conditions))
        
        if is_active is not None:
            conditions.append(UserModel.is_active == is_active)
        
        if is_superuser is not None:
            conditions.append(UserModel.is_superuser == is_superuser)
        
        if is_verified is not None:
            conditions.append(UserModel.is_verified == is_verified)
        
        # 构建查询
        base_query = select(UserModel).options(
            selectinload(UserModel.roles).selectinload(Role.permissions)
        )
        
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count(UserModel.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = base_query.order_by(UserModel.created_at.desc()).offset(skip).limit(limit)
        result = await self.db.execute(query)
        users = result.scalars().all()
        
        return users, total
    
    async def get_active_users_count(self) -> int:
        """获取活跃用户数量"""
        result = await self.db.execute(
            select(func.count(UserModel.id)).where(UserModel.is_active == True)
        )
        return result.scalar()
    
    async def get_verified_users_count(self) -> int:
        """获取已验证用户数量"""
        result = await self.db.execute(
            select(func.count(UserModel.id)).where(UserModel.is_verified == True)
        )
        return result.scalar()
    
    async def get_locked_users_count(self) -> int:
        """获取被锁定用户数量"""
        from datetime import datetime
        result = await self.db.execute(
            select(func.count(UserModel.id)).where(
                and_(
                    UserModel.locked_until.isnot(None),
                    UserModel.locked_until > datetime.utcnow()
                )
            )
        )
        return result.scalar()
    
    async def get_users_by_role(self, role_name: str) -> List[UserModel]:
        """根据角色获取用户列表"""
        result = await self.db.execute(
            select(UserModel)
            .join(UserModel.roles)
            .where(Role.name == role_name)
            .options(selectinload(UserModel.roles))
        )
        return result.scalars().all()
    
    async def update_last_login(self, user_id: UUID) -> bool:
        """更新用户最后登录时间"""
        from datetime import datetime
        
        result = await self.db.execute(
            select(UserModel).where(UserModel.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.last_login = datetime.utcnow()
            user.failed_login_attempts = 0  # 重置失败登录次数
            await self.db.commit()
            return True
        
        return False
    
    async def increment_failed_login_attempts(self, user_id: UUID) -> int:
        """增加失败登录次数"""
        result = await self.db.execute(
            select(UserModel).where(UserModel.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.failed_login_attempts += 1
            await self.db.commit()
            return user.failed_login_attempts
        
        return 0
    
    async def lock_user(self, user_id: UUID, lock_until: Optional[datetime] = None) -> bool:
        """锁定用户"""
        from datetime import datetime, timedelta
        
        if lock_until is None:
            lock_until = datetime.utcnow() + timedelta(hours=1)  # 默认锁定1小时
        
        result = await self.db.execute(
            select(UserModel).where(UserModel.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.locked_until = lock_until
            await self.db.commit()
            return True
        
        return False
    
    async def unlock_user(self, user_id: UUID) -> bool:
        """解锁用户"""
        result = await self.db.execute(
            select(UserModel).where(UserModel.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.locked_until = None
            user.failed_login_attempts = 0
            await self.db.commit()
            return True
        
        return False
    
    async def verify_user(self, user_id: UUID) -> bool:
        """验证用户"""
        result = await self.db.execute(
            select(UserModel).where(UserModel.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.is_verified = True
            await self.db.commit()
            return True
        
        return False
    
    async def add_role_to_user(self, user_id: UUID, role_id: UUID) -> bool:
        """为用户添加角色"""
        user = await self.get_with_roles(user_id)
        if not user:
            return False
        
        # 检查角色是否存在
        role_result = await self.db.execute(
            select(Role).where(Role.id == role_id)
        )
        role = role_result.scalar_one_or_none()
        if not role:
            return False
        
        # 检查用户是否已有该角色
        if role not in user.roles:
            user.roles.append(role)
            await self.db.commit()
        
        return True
    
    async def remove_role_from_user(self, user_id: UUID, role_id: UUID) -> bool:
        """从用户移除角色"""
        user = await self.get_with_roles(user_id)
        if not user:
            return False
        
        # 查找并移除角色
        for role in user.roles:
            if role.id == role_id:
                user.roles.remove(role)
                await self.db.commit()
                return True
        
        return False
    
    async def get_user_activities(
        self,
        user_id: UUID,
        *,
        skip: int = 0,
        limit: int = 100,
        action_filter: Optional[str] = None
    ) -> Tuple[List[UserActivity], int]:
        """获取用户活动记录"""
        conditions = [UserActivity.user_id == user_id]
        
        if action_filter:
            conditions.append(UserActivity.action.ilike(f"%{action_filter}%"))
        
        # 获取总数
        count_query = select(func.count(UserActivity.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = (
            select(UserActivity)
            .where(and_(*conditions))
            .order_by(UserActivity.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        activities = result.scalars().all()
        
        return activities, total
    
    async def create_user_activity(
        self,
        user_id: UUID,
        action: str,
        resource: Optional[str] = None,
        details: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> UserActivity:
        """创建用户活动记录"""
        activity = UserActivity(
            user_id=user_id,
            action=action,
            resource=resource,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(activity)
        await self.db.commit()
        await self.db.refresh(activity)
        
        return activity
