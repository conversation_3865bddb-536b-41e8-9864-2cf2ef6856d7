import React, { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, Button, Select, Space, Typography, Alert, Spin } from 'antd'
import {
  ReloadOutlined,
  FullscreenOutlined,
  SettingOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  LineChartOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { usdtApi } from '../../services/api/usdt'
import { PriceChart } from '../../components/charts/PriceChart'
import { StabilityIndicator } from '../../components/indicators/StabilityIndicator'

const { Title, Text } = Typography
const { Option } = Select

export default function Monitor() {
  const [period, setPeriod] = useState('24h')
  const [interval, setInterval] = useState('1h')
  const [chartType, setChartType] = useState<'line' | 'area' | 'composed'>('area')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // 获取当前数据
  const {
    data: currentData,
    isPending: currentLoading,
    refetch: refetchCurrent
  } = useQuery({
    queryKey: ['usdt', 'current'],
    queryFn: () => usdtApi.getCurrentData(),
    refetchInterval: autoRefresh ? 30000 : false,
  })

  // 获取图表数据
  const {
    data: chartData,
    isPending: chartLoading,
    refetch: refetchChart
  } = useQuery({
    queryKey: ['usdt', 'chart', period, interval],
    queryFn: () => usdtApi.getChartData(period, interval),
    refetchInterval: autoRefresh ? 300000 : false,
  })

  // 获取统计数据
  const {
    data: statsData,
    isPending: statsLoading,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['usdt', 'stats', period],
    queryFn: () => usdtApi.getStats(period),
    refetchInterval: autoRefresh ? 60000 : false,
  })

  const current = currentData?.data
  const chart = chartData?.data || []
  const stats = statsData?.data

  // 手动刷新
  const handleRefresh = async () => {
    await Promise.all([
      refetchCurrent(),
      refetchChart(),
      refetchStats()
    ])
  }

  // 获取价格趋势
  const getPriceTrend = () => {
    if (!stats?.price_change) return 'stable'
    return stats.price_change > 0 ? 'up' : 'down'
  }

  // 获取趋势颜色
  const getTrendColor = () => {
    const trend = getPriceTrend()
    switch (trend) {
      case 'up': return 'text-green-500'
      case 'down': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            USDT实时监控
          </Title>
          <Text type="secondary">
            实时监控USDT价格变化和市场动态
          </Text>
        </div>
        
        <Space>
          <Select
            value={period}
            onChange={setPeriod}
            style={{ width: 100 }}
          >
            <Option value="1h">1小时</Option>
            <Option value="24h">24小时</Option>
            <Option value="7d">7天</Option>
            <Option value="30d">30天</Option>
          </Select>
          
          <Select
            value={interval}
            onChange={setInterval}
            style={{ width: 100 }}
          >
            <Option value="1m">1分钟</Option>
            <Option value="5m">5分钟</Option>
            <Option value="15m">15分钟</Option>
            <Option value="1h">1小时</Option>
            <Option value="4h">4小时</Option>
            <Option value="1d">1天</Option>
          </Select>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={currentLoading || chartLoading || statsLoading}
          >
            刷新
          </Button>
          
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      </div>

      {/* 实时数据卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="当前价格"
              value={current?.current_price_usd || 0}
              precision={6}
              prefix={<DollarOutlined />}
              suffix="USD"
              loading={currentLoading}
              valueStyle={{ 
                color: current?.is_anomaly ? '#ff4d4f' : '#1890ff',
                fontSize: '28px'
              }}
            />
            {current?.is_anomaly && (
              <Alert
                message="价格异常"
                type="warning"
                size="small"
                showIcon
                className="mt-2"
              />
            )}
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={`${period}变化`}
              value={stats?.price_change_percentage || 0}
              precision={4}
              suffix="%"
              prefix={
                getPriceTrend() === 'up' ?
                <RiseOutlined /> :
                getPriceTrend() === 'down' ?
                <FallOutlined /> :
                <LineChartOutlined />
              }
              loading={statsLoading}
              valueStyle={{ 
                color: getTrendColor().replace('text-', ''),
                fontSize: '24px'
              }}
            />
            <div className="mt-2">
              <Text className={getTrendColor()}>
                {stats?.price_change && (
                  <>
                    {stats.price_change > 0 ? '+' : ''}
                    ${stats.price_change.toFixed(6)}
                  </>
                )}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="波动率"
              value={(stats?.volatility || 0) * 100}
              precision={4}
              suffix="%"
              loading={statsLoading}
              valueStyle={{ fontSize: '24px' }}
            />
            <div className="mt-2">
              <Text type="secondary" className="text-sm">
                价格范围: ${stats?.price_range?.toFixed(6) || '0'}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="稳定性评分"
              value={stats?.stability_score || 0}
              precision={3}
              suffix="/1.0"
              loading={statsLoading}
              valueStyle={{ 
                color: (stats?.stability_score || 0) > 0.8 ? '#52c41a' : 
                       (stats?.stability_score || 0) > 0.6 ? '#faad14' : '#ff4d4f',
                fontSize: '24px'
              }}
            />
            <div className="mt-2">
              <StabilityIndicator score={stats?.stability_score || 0} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 价格图表 */}
      <Card 
        title={
          <div className="flex items-center justify-between">
            <span>价格走势图</span>
            <Space>
              <Select
                value={chartType}
                onChange={setChartType}
                size="small"
                style={{ width: 80 }}
              >
                <Option value="line">线图</Option>
                <Option value="area">面积图</Option>
                <Option value="composed">组合图</Option>
              </Select>
              <Button 
                size="small" 
                icon={<FullscreenOutlined />}
                onClick={() => {
                  // 全屏显示图表
                }}
              >
                全屏
              </Button>
            </Space>
          </div>
        }
        loading={chartLoading}
      >
        {chart.length > 0 ? (
          <PriceChart 
            data={chart}
            height={500}
            type={chartType}
            showVolume={chartType === 'composed'}
          />
        ) : (
          <div className="flex items-center justify-center h-96">
            <Spin size="large" tip="加载图表数据..." />
          </div>
        )}
      </Card>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="价格统计" size="small">
            <div className="space-y-3">
              <div className="flex justify-between">
                <Text type="secondary">最高价:</Text>
                <Text strong>${stats?.max_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">最低价:</Text>
                <Text strong>${stats?.min_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">平均价:</Text>
                <Text strong>${stats?.avg_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">数据点数:</Text>
                <Text strong>{stats?.total_data_points || 0}</Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title="市场信息" size="small">
            <div className="space-y-3">
              <div className="flex justify-between">
                <Text type="secondary">数据源:</Text>
                <Text strong>{current?.data_source || 'N/A'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">最后更新:</Text>
                <Text strong>
                  {current?.last_updated ? 
                    new Date(current.last_updated).toLocaleString() : 'N/A'}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">偏离锚定:</Text>
                <Text strong>
                  {stats?.deviation_from_peg ? 
                    `${(stats.deviation_from_peg * 100).toFixed(4)}%` : 'N/A'}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">自动刷新:</Text>
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  {autoRefresh ? '已开启' : '已关闭'}
                </Button>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}
