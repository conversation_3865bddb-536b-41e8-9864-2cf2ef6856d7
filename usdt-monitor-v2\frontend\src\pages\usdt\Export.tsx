import React, { useState } from 'react'
import {
  Card,
  Typography,
  Form,
  DatePicker,
  Select,
  Checkbox,
  Button,
  Space,
  Row,
  Col,
  Alert,
  Table,
  Progress,
  Tag,
  message,
  Divider,
  Tooltip
} from 'antd'
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  DeleteOutlined,
  EyeOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import { usdtApi } from '../../services/api/usdt'
import type { USDTExportRequest, ExportTask } from '../../types/usdt'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

// 可导出的字段
const EXPORT_FIELDS = [
  { label: '时间戳', value: 'timestamp', required: true },
  { label: '价格 (USD)', value: 'current_price_usd', required: true },
  { label: '24h变化', value: 'price_change_24h' },
  { label: '24h变化百分比', value: 'price_change_percentage_24h' },
  { label: '7d变化', value: 'price_change_7d' },
  { label: '交易量', value: 'volume_24h' },
  { label: '市值', value: 'market_cap_usd' },
  { label: '数据源', value: 'data_source' },
  { label: '置信度', value: 'confidence_score' },
  { label: '是否异常', value: 'is_anomaly' }
]

export default function Export() {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'timestamp',
    'current_price_usd',
    'price_change_percentage_24h',
    'volume_24h',
    'data_source'
  ])

  // 获取导出任务列表
  const { data: exportTasks, isPending: tasksLoading } = useQuery({
    queryKey: ['export', 'tasks'],
    queryFn: () => usdtApi.getExportTasks?.() || Promise.resolve({ data: [] }),
    refetchInterval: 5000 // 5秒刷新一次
  })

  // 创建导出任务
  const createExportMutation = useMutation({
    mutationFn: (exportRequest: USDTExportRequest) => usdtApi.exportData(exportRequest),
    onSuccess: () => {
      message.success('导出任务已创建，请稍后查看进度')
      queryClient.invalidateQueries({ queryKey: ['export', 'tasks'] })
      form.resetFields()
    },
    onError: () => {
      message.error('创建导出任务失败，请重试')
    }
  })

  // 下载导出文件
  const downloadMutation = useMutation({
    mutationFn: ({ exportId, filename }: { exportId: string; filename: string }) =>
      usdtApi.downloadExport(exportId, filename),
    onSuccess: () => {
      message.success('文件下载已开始')
    },
    onError: () => {
      message.error('文件下载失败')
    }
  })

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    const exportRequest: USDTExportRequest = {
      start_date: values.dateRange[0].format('YYYY-MM-DD'),
      end_date: values.dateRange[1].format('YYYY-MM-DD'),
      format: values.format,
      interval: values.interval,
      fields: selectedFields,
      include_metadata: values.include_metadata
    }

    createExportMutation.mutate(exportRequest)
  }

  // 获取格式图标
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <FileTextOutlined />
      case 'xlsx':
        return <FileExcelOutlined />
      case 'json':
        return <FileTextOutlined />
      default:
        return <FileTextOutlined />
    }
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'blue', text: '等待中' },
      processing: { color: 'orange', text: '处理中' },
      completed: { color: 'green', text: '已完成' },
      failed: { color: 'red', text: '失败' }
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 导出任务表格列
  const taskColumns: ColumnsType<ExportTask> = [
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('MM-DD HH:mm')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(time).fromNow()}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: ExportTask) => (
        <div>
          <Progress
            percent={progress}
            size="small"
            status={record.status === 'failed' ? 'exception' : undefined}
          />
        </div>
      )
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size: number) => {
        if (!size) return '-'
        return size > 1024 * 1024
          ? `${(size / 1024 / 1024).toFixed(2)} MB`
          : `${(size / 1024).toFixed(2)} KB`
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: ExportTask) => (
        <Space>
          {record.status === 'completed' && (
            <Tooltip title="下载文件">
              <Button
                type="link"
                size="small"
                icon={<DownloadOutlined />}
                onClick={() => downloadMutation.mutate({
                  exportId: record.export_id,
                  filename: `usdt_export_${record.export_id}.${record.format || 'csv'}`
                })}
                loading={downloadMutation.isPending}
              />
            </Tooltip>
          )}
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                // 显示导出详情
              }}
            />
          </Tooltip>
          <Tooltip title="删除任务">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                // 删除导出任务
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="mb-2">
          数据导出
        </Title>
        <Text type="secondary">
          导出USDT历史数据到不同格式的文件
        </Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 导出配置 */}
        <Col xs={24} lg={10}>
          <Card title="导出配置" className="h-full">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                dateRange: [dayjs().subtract(7, 'day'), dayjs()],
                format: 'csv',
                interval: '1h',
                include_metadata: true
              }}
            >
              <Form.Item
                name="dateRange"
                label="时间范围"
                rules={[{ required: true, message: '请选择时间范围' }]}
              >
                <RangePicker
                  style={{ width: '100%' }}
                  disabledDate={(current) => current && current > dayjs().endOf('day')}
                />
              </Form.Item>

              <Form.Item
                name="format"
                label="导出格式"
                rules={[{ required: true, message: '请选择导出格式' }]}
              >
                <Select>
                  <Option value="csv">
                    <Space>
                      <FileTextOutlined />
                      CSV 格式
                    </Space>
                  </Option>
                  <Option value="xlsx">
                    <Space>
                      <FileExcelOutlined />
                      Excel 格式
                    </Space>
                  </Option>
                  <Option value="json">
                    <Space>
                      <FileTextOutlined />
                      JSON 格式
                    </Space>
                  </Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="interval"
                label="数据间隔"
                rules={[{ required: true, message: '请选择数据间隔' }]}
              >
                <Select>
                  <Option value="1m">1分钟</Option>
                  <Option value="5m">5分钟</Option>
                  <Option value="15m">15分钟</Option>
                  <Option value="1h">1小时</Option>
                  <Option value="4h">4小时</Option>
                  <Option value="1d">1天</Option>
                </Select>
              </Form.Item>

              <Form.Item label="导出字段">
                <Checkbox.Group
                  value={selectedFields}
                  onChange={setSelectedFields}
                  className="w-full"
                >
                  <Row gutter={[8, 8]}>
                    {EXPORT_FIELDS.map(field => (
                      <Col span={12} key={field.value}>
                        <Checkbox
                          value={field.value}
                          disabled={field.required}
                        >
                          {field.label}
                          {field.required && (
                            <Text type="secondary" className="ml-1">*</Text>
                          )}
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item name="include_metadata" valuePropName="checked">
                <Checkbox>包含元数据信息</Checkbox>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<DownloadOutlined />}
                  loading={createExportMutation.isPending}
                  block
                >
                  创建导出任务
                </Button>
              </Form.Item>
            </Form>

            <Divider />

            <Alert
              message="导出说明"
              description={
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• 大量数据导出可能需要较长时间</li>
                  <li>• 导出文件将在服务器保存7天</li>
                  <li>• 单次导出最多支持100万条记录</li>
                  <li>• CSV格式适合数据分析</li>
                  <li>• Excel格式支持图表和格式</li>
                  <li>• JSON格式适合程序处理</li>
                </ul>
              }
              type="info"
              showIcon
            />
          </Card>
        </Col>

        {/* 导出任务列表 */}
        <Col xs={24} lg={14}>
          <Card
            title={
              <div className="flex items-center justify-between">
                <span>导出任务</span>
                <Space>
                  <ClockCircleOutlined />
                  <Text type="secondary">自动刷新</Text>
                </Space>
              </div>
            }
            className="h-full"
          >
            <Table
              columns={taskColumns}
              dataSource={exportTasks?.data || []}
              rowKey="export_id"
              loading={tasksLoading}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: false
              }}
              size="small"
              locale={{
                emptyText: '暂无导出任务'
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
