"""
USDT数据服务
"""
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.cache import cache_manager
from app.core.exceptions import AppException
from app.domain.schemas.usdt import (
    USDTData, USDTStats, USDTAlert, USDTAlertCreate, USDTDataResponse
)
from app.infrastructure.repositories.usdt_repository import USDTDataRepository


class USDTService:
    """USDT数据服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = USDTDataRepository(db)
    
    async def get_current_data(self) -> Optional[USDTData]:
        """获取当前USDT数据"""
        try:
            # 先尝试从缓存获取
            cache_key = "usdt:current_data"
            cached_data = await cache_manager.get(cache_key)
            
            if cached_data:
                return USDTData.parse_obj(cached_data)
            
            # 从数据库获取最新数据
            latest_data = await self.repository.get_latest_data()
            
            if latest_data:
                usdt_data = USDTData.from_orm(latest_data)
                
                # 缓存数据（缓存30秒）
                await cache_manager.set(
                    cache_key, 
                    usdt_data.dict(), 
                    expire=30
                )
                
                return usdt_data
            
            return None
            
        except Exception as e:
            raise AppException(
                error_code="CURRENT_DATA_FETCH_ERROR",
                message="获取当前数据失败",
                details={"error": str(e)}
            )
    
    async def get_history_data(
        self,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1h",
        source: Optional[str] = None,
        offset: int = 0,
        limit: int = 1000
    ) -> Tuple[List[USDTData], int]:
        """获取历史数据"""
        try:
            # 验证时间范围
            if start_date >= end_date:
                raise AppException(
                    error_code="INVALID_DATE_RANGE",
                    message="开始时间必须早于结束时间"
                )
            
            # 限制查询范围
            max_range = timedelta(days=90)
            if end_date - start_date > max_range:
                raise AppException(
                    error_code="DATE_RANGE_TOO_LARGE",
                    message="查询时间范围不能超过90天"
                )
            
            # 计算间隔分钟数
            interval_minutes = self._parse_interval(interval)
            
            # 构建缓存键
            cache_key = f"usdt:history:{start_date.isoformat()}:{end_date.isoformat()}:{interval}:{source}:{offset}:{limit}"
            cached_data = await cache_manager.get(cache_key)
            
            if cached_data:
                return (
                    [USDTData.parse_obj(item) for item in cached_data['items']],
                    cached_data['total']
                )
            
            # 从数据库获取数据
            data_models, total = await self.repository.get_data_by_time_range(
                start_time=start_date,
                end_time=end_date,
                source=source,
                interval_minutes=interval_minutes,
                skip=offset,
                limit=limit
            )
            
            # 转换为schema
            usdt_data_list = [USDTData.from_orm(model) for model in data_models]
            
            # 缓存结果（缓存5分钟）
            cache_data = {
                'items': [item.dict() for item in usdt_data_list],
                'total': total
            }
            await cache_manager.set(cache_key, cache_data, expire=300)
            
            return usdt_data_list, total
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="HISTORY_DATA_FETCH_ERROR",
                message="获取历史数据失败",
                details={"error": str(e)}
            )
    
    async def get_stats(self, period: str = "24h") -> USDTStats:
        """获取统计数据"""
        try:
            # 解析时间周期
            end_time = datetime.utcnow()
            start_time = self._parse_period(period, end_time)
            
            # 构建缓存键
            cache_key = f"usdt:stats:{period}"
            cached_stats = await cache_manager.get(cache_key)
            
            if cached_stats:
                return USDTStats.parse_obj(cached_stats)
            
            # 获取统计数据
            stats_data = await self.repository.get_price_statistics(
                start_time=start_time,
                end_time=end_time
            )
            
            if stats_data['count'] == 0:
                raise AppException(
                    error_code="NO_DATA_AVAILABLE",
                    message="指定时间段内没有数据"
                )
            
            # 获取当前价格
            current_data = await self.get_current_data()
            current_price = current_data.current_price_usd if current_data else 1.0
            
            # 计算价格变化
            price_changes = await self.repository.get_price_changes(
                current_time=end_time,
                periods=[period]
            )
            
            period_change = price_changes.get(period, {})
            
            # 计算稳定性评分
            stability_score = self._calculate_stability_score(
                stats_data['volatility'],
                abs(float(current_price) - 1.0)
            )
            
            # 构建统计对象
            stats = USDTStats(
                period=period,
                current_price=current_price,
                avg_price=stats_data['avg_price'],
                min_price=stats_data['min_price'],
                max_price=stats_data['max_price'],
                price_range=stats_data['price_range'],
                volatility=stats_data['volatility'] or 0,
                total_data_points=stats_data['count'],
                price_change=period_change.get('price_change'),
                price_change_percentage=period_change.get('price_change_percentage'),
                avg_volume=stats_data['avg_volume'],
                total_volume=stats_data['total_volume'],
                stability_score=stability_score,
                deviation_from_peg=(float(current_price) - 1.0) * 100,
                start_time=start_time,
                end_time=end_time
            )
            
            # 缓存统计数据（缓存2分钟）
            await cache_manager.set(cache_key, stats.dict(), expire=120)
            
            return stats
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="STATS_CALCULATION_ERROR",
                message="计算统计数据失败",
                details={"error": str(e)}
            )
    
    async def get_chart_data(self, period: str, interval: str) -> List[USDTDataResponse]:
        """获取图表数据"""
        try:
            # 解析时间周期
            end_time = datetime.utcnow()
            start_time = self._parse_period(period, end_time)
            
            # 计算间隔分钟数
            interval_minutes = self._parse_interval(interval)
            
            # 构建缓存键
            cache_key = f"usdt:chart:{period}:{interval}"
            cached_data = await cache_manager.get(cache_key)
            
            if cached_data:
                return [USDTDataResponse.parse_obj(item) for item in cached_data]
            
            # 获取图表数据
            chart_data = await self.repository.get_chart_data(
                start_time=start_time,
                end_time=end_time,
                interval_minutes=interval_minutes
            )
            
            # 转换为响应格式
            response_data = []
            for data_point in chart_data:
                response_data.append(USDTDataResponse(
                    timestamp=data_point['timestamp'],
                    price=data_point['price'],
                    price_change_24h=None,  # 图表数据中暂不包含变化数据
                    volume_24h=data_point['volume'],
                    market_cap=None
                ))
            
            # 缓存图表数据（缓存1分钟）
            cache_data = [item.dict() for item in response_data]
            await cache_manager.set(cache_key, cache_data, expire=60)
            
            return response_data
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="CHART_DATA_FETCH_ERROR",
                message="获取图表数据失败",
                details={"error": str(e)}
            )
    
    async def create_alert(self, user_id: UUID, alert_data: USDTAlertCreate) -> USDTAlert:
        """创建价格告警"""
        try:
            # TODO: 实现告警创建逻辑
            # 这里需要创建告警仓储和模型
            
            # 临时返回示例数据
            from uuid import uuid4
            alert = USDTAlert(
                id=uuid4(),
                user_id=user_id,
                alert_type=alert_data.alert_type,
                threshold_value=alert_data.threshold_value,
                is_active=alert_data.is_active,
                description=alert_data.description,
                triggered_count=0,
                last_triggered=None,
                created_at=datetime.utcnow(),
                updated_at=None
            )
            
            return alert
            
        except Exception as e:
            raise AppException(
                error_code="ALERT_CREATE_ERROR",
                message="创建告警失败",
                details={"error": str(e)}
            )
    
    async def get_user_alerts(
        self,
        user_id: UUID,
        is_active: Optional[bool] = None,
        offset: int = 0,
        limit: int = 100
    ) -> Tuple[List[USDTAlert], int]:
        """获取用户告警列表"""
        try:
            # TODO: 实现告警查询逻辑
            # 临时返回空列表
            return [], 0
            
        except Exception as e:
            raise AppException(
                error_code="ALERT_FETCH_ERROR",
                message="获取告警列表失败",
                details={"error": str(e)}
            )
    
    async def delete_alert(self, alert_id: UUID, user_id: UUID) -> bool:
        """删除告警"""
        try:
            # TODO: 实现告警删除逻辑
            return True
            
        except Exception as e:
            raise AppException(
                error_code="ALERT_DELETE_ERROR",
                message="删除告警失败",
                details={"error": str(e)}
            )
    
    def _parse_interval(self, interval: str) -> int:
        """解析时间间隔为分钟数"""
        interval_map = {
            "1m": 1,
            "5m": 5,
            "15m": 15,
            "1h": 60,
            "4h": 240,
            "1d": 1440
        }
        
        if interval not in interval_map:
            raise AppException(
                error_code="INVALID_INTERVAL",
                message=f"无效的时间间隔: {interval}"
            )
        
        return interval_map[interval]
    
    def _parse_period(self, period: str, end_time: datetime) -> datetime:
        """解析时间周期"""
        period_map = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        
        if period not in period_map:
            raise AppException(
                error_code="INVALID_PERIOD",
                message=f"无效的时间周期: {period}"
            )
        
        return end_time - period_map[period]
    
    def _calculate_stability_score(self, volatility: float, deviation: float) -> float:
        """计算稳定性评分"""
        try:
            # 基于波动性和偏差计算稳定性评分
            # 评分范围: 0.0 - 1.0，1.0表示最稳定
            
            volatility = volatility or 0
            deviation = abs(deviation)
            
            # 波动性权重 (0-1)
            volatility_score = max(0, 1 - volatility * 10)
            
            # 偏差权重 (0-1)
            deviation_score = max(0, 1 - deviation * 20)
            
            # 综合评分
            stability_score = (volatility_score * 0.6 + deviation_score * 0.4)
            
            return min(1.0, max(0.0, stability_score))
            
        except Exception:
            return 0.5  # 默认中等稳定性
