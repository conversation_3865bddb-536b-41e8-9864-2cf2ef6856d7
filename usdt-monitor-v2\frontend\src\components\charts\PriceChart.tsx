import React, { useMemo } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart, ComposedChart, Bar } from 'recharts'
import { Card, Select, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import type { USDTChartData } from '../../types/usdt'

const { Text } = Typography

interface PriceChartProps {
  data: USDTChartData[]
  height?: number
  showVolume?: boolean
  type?: 'line' | 'area' | 'composed'
  className?: string
}

export const PriceChart: React.FC<PriceChartProps> = ({
  data,
  height = 300,
  showVolume = false,
  type = 'line',
  className
}) => {
  // 处理图表数据
  const chartData = useMemo(() => {
    return data.map(item => ({
      ...item,
      timestamp: dayjs(item.timestamp).valueOf(),
      formattedTime: dayjs(item.timestamp).format('HH:mm'),
      formattedDate: dayjs(item.timestamp).format('MM-DD HH:mm'),
      price: Number(item.price),
      volume: item.volume ? Number(item.volume) : 0,
    }))
  }, [data])

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {dayjs(label).format('YYYY-MM-DD HH:mm:ss')}
          </p>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">价格:</span>
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                ${data.price.toFixed(6)}
              </span>
            </div>
            {showVolume && data.volume > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">交易量:</span>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  ${(data.volume / 1e9).toFixed(2)}B
                </span>
              </div>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  // 格式化X轴标签
  const formatXAxisLabel = (tickItem: number) => {
    return dayjs(tickItem).format('HH:mm')
  }

  // 格式化Y轴标签
  const formatYAxisLabel = (value: number) => {
    return `$${value.toFixed(4)}`
  }

  // 渲染不同类型的图表
  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    }

    switch (type) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <defs>
              <linearGradient id="priceGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#1890ff" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#1890ff" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={formatXAxisLabel}
              stroke="#666"
            />
            <YAxis 
              domain={['dataMin - 0.001', 'dataMax + 0.001']}
              tickFormatter={formatYAxisLabel}
              stroke="#666"
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="price"
              stroke="#1890ff"
              strokeWidth={2}
              fill="url(#priceGradient)"
            />
          </AreaChart>
        )

      case 'composed':
        return (
          <ComposedChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={formatXAxisLabel}
              stroke="#666"
            />
            <YAxis 
              yAxisId="price"
              domain={['dataMin - 0.001', 'dataMax + 0.001']}
              tickFormatter={formatYAxisLabel}
              stroke="#666"
            />
            {showVolume && (
              <YAxis 
                yAxisId="volume"
                orientation="right"
                tickFormatter={(value) => `${(value / 1e9).toFixed(1)}B`}
                stroke="#666"
              />
            )}
            <Tooltip content={<CustomTooltip />} />
            <Line
              yAxisId="price"
              type="monotone"
              dataKey="price"
              stroke="#1890ff"
              strokeWidth={2}
              dot={false}
            />
            {showVolume && (
              <Bar
                yAxisId="volume"
                dataKey="volume"
                fill="#52c41a"
                opacity={0.3}
              />
            )}
          </ComposedChart>
        )

      default: // line
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={formatXAxisLabel}
              stroke="#666"
            />
            <YAxis 
              domain={['dataMin - 0.001', 'dataMax + 0.001']}
              tickFormatter={formatYAxisLabel}
              stroke="#666"
            />
            <Tooltip content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="price"
              stroke="#1890ff"
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, stroke: '#1890ff', strokeWidth: 2 }}
            />
          </LineChart>
        )
    }
  }

  if (!chartData.length) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <Text type="secondary">暂无图表数据</Text>
      </div>
    )
  }

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height={height}>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  )
}
