import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Badge,
  Space,
  Tooltip,
  Switch,
  Divider,
} from 'antd'
import {
  DashboardOutlined,
  LineChartOutlined,
  HistoryOutlined,
  BellOutlined,
  ExportOutlined,
  UserOutlined,
  SettingOutlined,
  TeamOutlined,
  SystemOutlined,
  FileTextOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SunOutlined,
  MoonOutlined,
} from '@ant-design/icons'

import { useAuthStore } from '../stores/authStore'
import { useThemeStore } from '../stores/themeStore'

const { Header, Sider, Content } = Layout

// 菜单项配置
const getMenuItems = (isAdmin: boolean) => [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/usdt',
    icon: <LineChartOutlined />,
    label: 'USDT监控',
    children: [
      {
        key: '/usdt/monitor',
        icon: <LineChartOutlined />,
        label: '实时监控',
      },
      {
        key: '/usdt/history',
        icon: <HistoryOutlined />,
        label: '历史数据',
      },
      {
        key: '/usdt/alerts',
        icon: <BellOutlined />,
        label: '价格告警',
      },
      {
        key: '/usdt/export',
        icon: <ExportOutlined />,
        label: '数据导出',
      },
    ],
  },
  {
    key: '/user',
    icon: <UserOutlined />,
    label: '用户中心',
    children: [
      {
        key: '/user/profile',
        icon: <UserOutlined />,
        label: '个人资料',
      },
      {
        key: '/user/settings',
        icon: <SettingOutlined />,
        label: '账户设置',
      },
    ],
  },
  ...(isAdmin ? [
    {
      key: '/admin',
      icon: <SystemOutlined />,
      label: '系统管理',
      children: [
        {
          key: '/admin/users',
          icon: <TeamOutlined />,
          label: '用户管理',
        },
        {
          key: '/admin/system',
          icon: <SystemOutlined />,
          label: '系统设置',
        },
        {
          key: '/admin/logs',
          icon: <FileTextOutlined />,
          label: '系统日志',
        },
      ],
    },
  ] : []),
]

export const MainLayout: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, isAdmin } = useAuthStore()
  const { 
    collapsed, 
    toggleCollapsed, 
    theme, 
    setTheme, 
    isDark 
  } = useThemeStore()

  const [notifications] = useState(3) // 模拟通知数量

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/user/profile')
        break
      case 'settings':
        navigate('/user/settings')
        break
      case 'logout':
        logout()
        navigate('/auth/login')
        break
    }
  }

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ]

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname
    if (path === '/') return ['/']
    
    // 匹配最具体的路径
    const menuItems = getMenuItems(isAdmin())
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (path.startsWith(child.key)) {
            return [child.key]
          }
        }
      } else if (path.startsWith(item.key)) {
        return [item.key]
      }
    }
    
    return []
  }

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname
    const openKeys: string[] = []
    
    const menuItems = getMenuItems(isAdmin())
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (path.startsWith(child.key)) {
            openKeys.push(item.key)
            break
          }
        }
      }
    }
    
    return openKeys
  }

  return (
    <Layout className="min-h-screen">
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={256}
        className="shadow-lg"
        theme={isDark ? 'dark' : 'light'}
      >
        {/* Logo */}
        <div className="h-16 flex items-center justify-center border-b border-gray-200 dark:border-gray-700">
          {collapsed ? (
            <div className="text-2xl font-bold text-primary">U</div>
          ) : (
            <div className="text-lg font-bold text-primary">USDT Monitor</div>
          )}
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={getMenuItems(isAdmin())}
          onClick={handleMenuClick}
          theme={isDark ? 'dark' : 'light'}
          className="border-r-0"
        />
      </Sider>

      <Layout>
        {/* 头部 */}
        <Header className="bg-white dark:bg-gray-800 shadow-sm px-4 flex items-center justify-between">
          {/* 左侧 */}
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleCollapsed}
              className="text-lg"
            />
          </div>

          {/* 右侧 */}
          <div className="flex items-center space-x-4">
            {/* 主题切换 */}
            <Tooltip title={isDark ? '切换到浅色模式' : '切换到深色模式'}>
              <Button
                type="text"
                icon={isDark ? <SunOutlined /> : <MoonOutlined />}
                onClick={() => setTheme(isDark ? 'light' : 'dark')}
              />
            </Tooltip>

            {/* 通知 */}
            <Tooltip title="通知">
              <Badge count={notifications} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  onClick={() => navigate('/notifications')}
                />
              </Badge>
            </Tooltip>

            <Divider type="vertical" />

            {/* 用户信息 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
              arrow
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded">
                <Avatar
                  size="small"
                  src={user?.avatar_url}
                  icon={<UserOutlined />}
                />
                <div className="hidden md:block">
                  <div className="text-sm font-medium">{user?.full_name}</div>
                  <div className="text-xs text-gray-500">{user?.email}</div>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content className="p-6 bg-gray-50 dark:bg-gray-900 min-h-[calc(100vh-64px)]">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm min-h-full">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}
