import { io, Socket } from 'socket.io-client'
import { message } from 'antd'
import type { USDTData, PriceAlert } from '../types/usdt'

// WebSocket事件类型
export interface WebSocketEvents {
  // 服务器发送的事件
  'usdt:price_update': (data: USDTData) => void
  'usdt:price_alert': (alert: PriceAlert) => void
  'usdt:anomaly_detected': (data: { data: USDTData; reason: string }) => void
  'system:notification': (notification: { type: string; message: string; data?: any }) => void
  'user:session_expired': () => void
  
  // 客户端发送的事件
  'subscribe:price_updates': () => void
  'unsubscribe:price_updates': () => void
  'subscribe:alerts': (userId: string) => void
  'unsubscribe:alerts': (userId: string) => void
}

export class WebSocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false
  private subscriptions = new Set<string>()
  private eventListeners = new Map<string, Function[]>()

  constructor() {
    this.connect()
  }

  // 连接WebSocket
  connect() {
    if (this.isConnecting || this.socket?.connected) {
      return
    }

    this.isConnecting = true
    
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000'
    const token = localStorage.getItem('auth-token')

    this.socket = io(wsUrl, {
      auth: {
        token
      },
      transports: ['websocket'],
      timeout: 20000,
      forceNew: true
    })

    this.setupEventListeners()
  }

  // 设置事件监听器
  private setupEventListeners() {
    if (!this.socket) return

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.isConnecting = false
      this.reconnectAttempts = 0
      
      // 重新订阅之前的频道
      this.resubscribe()
      
      message.success('实时连接已建立', 2)
    })

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      this.isConnecting = false
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不自动重连
        message.warning('服务器连接已断开')
      } else {
        // 网络问题等，尝试重连
        this.handleReconnect()
      }
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.isConnecting = false
      this.handleReconnect()
    })

    // 认证错误
    this.socket.on('auth_error', (error) => {
      console.error('WebSocket auth error:', error)
      message.error('认证失败，请重新登录')
      // 清除token并跳转到登录页
      localStorage.removeItem('auth-token')
      window.location.href = '/auth/login'
    })

    // USDT价格更新
    this.socket.on('usdt:price_update', (data: USDTData) => {
      this.emit('usdt:price_update', data)
    })

    // 价格告警
    this.socket.on('usdt:price_alert', (alert: PriceAlert) => {
      this.emit('usdt:price_alert', alert)
      
      // 显示告警通知
      message.warning({
        content: `价格告警: ${alert.message}`,
        duration: 5,
        key: `alert-${alert.alert_id}`
      })
    })

    // 异常检测
    this.socket.on('usdt:anomaly_detected', (data: { data: USDTData; reason: string }) => {
      this.emit('usdt:anomaly_detected', data)
      
      // 显示异常通知
      message.error({
        content: `检测到价格异常: ${data.reason}`,
        duration: 8,
        key: `anomaly-${data.data.id}`
      })
    })

    // 系统通知
    this.socket.on('system:notification', (notification) => {
      this.emit('system:notification', notification)
      
      // 根据类型显示不同的通知
      const messageType = notification.type === 'error' ? 'error' : 
                         notification.type === 'warning' ? 'warning' : 'info'
      
      message[messageType](notification.message, 5)
    })

    // 会话过期
    this.socket.on('user:session_expired', () => {
      this.emit('user:session_expired')
      message.error('会话已过期，请重新登录')
      localStorage.removeItem('auth-token')
      window.location.href = '/auth/login'
    })
  }

  // 处理重连
  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      message.error('连接失败，请刷新页面重试')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      this.connect()
    }, delay)
  }

  // 重新订阅
  private resubscribe() {
    this.subscriptions.forEach(subscription => {
      if (this.socket) {
        this.socket.emit(subscription)
      }
    })
  }

  // 订阅价格更新
  subscribeToPriceUpdates() {
    if (this.socket?.connected) {
      this.socket.emit('subscribe:price_updates')
      this.subscriptions.add('subscribe:price_updates')
    }
  }

  // 取消订阅价格更新
  unsubscribeFromPriceUpdates() {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe:price_updates')
      this.subscriptions.delete('subscribe:price_updates')
    }
  }

  // 订阅用户告警
  subscribeToAlerts(userId: string) {
    if (this.socket?.connected) {
      this.socket.emit('subscribe:alerts', userId)
      this.subscriptions.add(`subscribe:alerts:${userId}`)
    }
  }

  // 取消订阅用户告警
  unsubscribeFromAlerts(userId: string) {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe:alerts', userId)
      this.subscriptions.delete(`subscribe:alerts:${userId}`)
    }
  }

  // 添加事件监听器
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  // 移除事件监听器
  off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // 触发事件
  private emit<K extends keyof WebSocketEvents>(event: K, ...args: Parameters<WebSocketEvents[K]>) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          // @ts-ignore
          listener(...args)
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error)
        }
      })
    }
  }

  // 获取连接状态
  isConnected(): boolean {
    return this.socket?.connected || false
  }

  // 获取连接ID
  getSocketId(): string | undefined {
    return this.socket?.id
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.subscriptions.clear()
    this.eventListeners.clear()
    this.isConnecting = false
    this.reconnectAttempts = 0
  }

  // 发送自定义事件
  emit(event: string, data?: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data)
    }
  }
}

// 创建全局WebSocket实例
export const websocketService = new WebSocketService()

// React Hook for WebSocket
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = React.useState(websocketService.isConnected())

  React.useEffect(() => {
    const handleConnect = () => setIsConnected(true)
    const handleDisconnect = () => setIsConnected(false)

    websocketService.on('connect' as any, handleConnect)
    websocketService.on('disconnect' as any, handleDisconnect)

    return () => {
      websocketService.off('connect' as any, handleConnect)
      websocketService.off('disconnect' as any, handleDisconnect)
    }
  }, [])

  return {
    isConnected,
    subscribe: websocketService.subscribeToPriceUpdates.bind(websocketService),
    unsubscribe: websocketService.unsubscribeFromPriceUpdates.bind(websocketService),
    subscribeToAlerts: websocketService.subscribeToAlerts.bind(websocketService),
    unsubscribeFromAlerts: websocketService.unsubscribeFromAlerts.bind(websocketService),
    on: websocketService.on.bind(websocketService),
    off: websocketService.off.bind(websocketService),
    emit: websocketService.emit.bind(websocketService)
  }
}
