import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form, Input, Button, Alert, Space, Typography, Result } from 'antd'
import { MailOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { authApi } from '../../services/api/auth'

const { Text } = Typography

export default function ForgotPassword() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [email, setEmail] = useState('')

  const handleSubmit = async (values: { email: string }) => {
    setLoading(true)
    setError(null)

    try {
      await authApi.forgotPassword({ email: values.email })
      setEmail(values.email)
      setSuccess(true)
    } catch (err: any) {
      setError(err.message || '发送重置邮件失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-6">
        <Result
          status="success"
          title="重置邮件已发送"
          subTitle={
            <div className="space-y-2">
              <p>我们已向 <strong>{email}</strong> 发送了密码重置邮件。</p>
              <p>请检查您的邮箱并点击邮件中的链接来重置密码。</p>
            </div>
          }
          extra={[
            <Button type="primary" key="login">
              <Link to="/auth/login">返回登录</Link>
            </Button>,
            <Button key="resend" onClick={() => setSuccess(false)}>
              重新发送
            </Button>,
          ]}
        />

        <div className="text-center">
          <Text type="secondary" className="text-sm">
            没有收到邮件？请检查垃圾邮件文件夹，或者{' '}
            <Button type="link" size="small" onClick={() => setSuccess(false)}>
              重新发送
            </Button>
          </Text>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 返回按钮 */}
      <div>
        <Link
          to="/auth/login"
          className="inline-flex items-center text-primary hover:text-primary-dark"
        >
          <ArrowLeftOutlined className="mr-2" />
          返回登录
        </Link>
      </div>

      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          忘记密码
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          输入您的邮箱地址，我们将发送重置密码的链接
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* 重置表单 */}
      <Form
        name="forgot-password"
        size="large"
        onFinish={handleSubmit}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="email"
          label="邮箱地址"
          rules={[
            { required: true, message: '请输入邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入注册时使用的邮箱地址"
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            size="large"
          >
            {loading ? '发送中...' : '发送重置邮件'}
          </Button>
        </Form.Item>
      </Form>

      {/* 其他选项 */}
      <div className="text-center space-y-4">
        <div>
          <Space>
            <Text type="secondary">记起密码了？</Text>
            <Link
              to="/auth/login"
              className="text-primary hover:text-primary-dark font-medium"
            >
              立即登录
            </Link>
          </Space>
        </div>

        <div>
          <Space>
            <Text type="secondary">还没有账户？</Text>
            <Link
              to="/auth/register"
              className="text-primary hover:text-primary-dark font-medium"
            >
              立即注册
            </Link>
          </Space>
        </div>
      </div>

      {/* 帮助信息 */}
      <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Text type="secondary" className="text-sm">
          <strong>提示：</strong>
        </Text>
        <ul className="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• 重置邮件可能需要几分钟才能到达</li>
          <li>• 请检查垃圾邮件文件夹</li>
          <li>• 重置链接24小时内有效</li>
          <li>• 如果仍有问题，请联系客服</li>
        </ul>
      </div>
    </div>
  )
}
