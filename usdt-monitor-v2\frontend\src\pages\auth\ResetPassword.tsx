import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { Form, Input, Button, Alert, Typography, Result } from 'antd'
import { LockOutlined, EyeInvisibleOutlined, EyeTwoTone, CheckCircleOutlined } from '@ant-design/icons'
import { authApi } from '../../services/api/auth'

const { Text } = Typography

export default function ResetPassword() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [tokenValid, setTokenValid] = useState<boolean | null>(null)

  const token = searchParams.get('token')

  useEffect(() => {
    // 检查token是否存在
    if (!token) {
      setError('重置链接无效或已过期')
      setTokenValid(false)
      return
    }

    // 这里可以添加token验证逻辑
    setTokenValid(true)
  }, [token])

  const handleSubmit = async (values: { new_password: string; confirm_password: string }) => {
    if (!token) {
      setError('重置链接无效')
      return
    }

    setLoading(true)
    setError(null)

    try {
      await authApi.resetPassword({
        token,
        new_password: values.new_password,
        confirm_password: values.confirm_password,
      })
      
      setSuccess(true)
      
      // 3秒后跳转到登录页
      setTimeout(() => {
        navigate('/auth/login', {
          state: { message: '密码重置成功，请使用新密码登录' }
        })
      }, 3000)
    } catch (err: any) {
      setError(err.message || '密码重置失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // token无效
  if (tokenValid === false) {
    return (
      <div className="space-y-6">
        <Result
          status="error"
          title="重置链接无效"
          subTitle="重置链接可能已过期或无效，请重新申请密码重置。"
          extra={[
            <Button type="primary" key="forgot">
              <Link to="/auth/forgot-password">重新申请</Link>
            </Button>,
            <Button key="login">
              <Link to="/auth/login">返回登录</Link>
            </Button>,
          ]}
        />
      </div>
    )
  }

  // 重置成功
  if (success) {
    return (
      <div className="space-y-6">
        <Result
          icon={<CheckCircleOutlined className="text-green-500" />}
          title="密码重置成功"
          subTitle="您的密码已成功重置，即将跳转到登录页面..."
          extra={[
            <Button type="primary" key="login" onClick={() => navigate('/auth/login')}>
              立即登录
            </Button>,
          ]}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          重置密码
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          请输入您的新密码
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* 重置表单 */}
      <Form
        name="reset-password"
        size="large"
        onFinish={handleSubmit}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="new_password"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 8, message: '密码至少8个字符' },
            { 
              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
              message: '密码必须包含大小写字母和数字' 
            },
          ]}
          hasFeedback
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
            autoComplete="new-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item
          name="confirm_password"
          label="确认新密码"
          dependencies={['new_password']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('new_password') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('两次输入的密码不一致'))
              },
            }),
          ]}
          hasFeedback
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入新密码"
            autoComplete="new-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            size="large"
          >
            {loading ? '重置中...' : '重置密码'}
          </Button>
        </Form.Item>
      </Form>

      {/* 登录链接 */}
      <div className="text-center">
        <Text type="secondary">
          记起密码了？{' '}
          <Link
            to="/auth/login"
            className="text-primary hover:text-primary-dark font-medium"
          >
            立即登录
          </Link>
        </Text>
      </div>

      {/* 密码要求提示 */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Text type="secondary" className="text-sm">
          <strong>新密码要求：</strong>
        </Text>
        <ul className="mt-1 text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• 至少8个字符</li>
          <li>• 包含大写字母</li>
          <li>• 包含小写字母</li>
          <li>• 包含数字</li>
        </ul>
      </div>
    </div>
  )
}
