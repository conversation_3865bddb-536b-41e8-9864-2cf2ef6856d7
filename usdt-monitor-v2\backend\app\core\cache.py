"""
缓存管理模块
"""
import json
import pickle
from typing import Any, Optional, Union

import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.config import settings


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._redis: Optional[Redis] = None
    
    async def connect(self) -> None:
        """连接Redis"""
        self._redis = redis.from_url(
            str(settings.REDIS_URL),
            encoding="utf-8",
            decode_responses=False,
            max_connections=20,
        )
    
    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
    
    @property
    def redis(self) -> Redis:
        """获取Redis实例"""
        if not self._redis:
            raise RuntimeError("Redis not connected. Call connect() first.")
        return self._redis
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{settings.CACHE_PREFIX}:{key}"
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            Any: 缓存值
        """
        try:
            value = await self.redis.get(self._make_key(key))
            if value is None:
                return default
            return pickle.loads(value)
        except Exception:
            return default
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否设置成功
        """
        try:
            ttl = ttl or settings.CACHE_TTL
            serialized_value = pickle.dumps(value)
            return await self.redis.setex(
                self._make_key(key), 
                ttl, 
                serialized_value
            )
        except Exception:
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否删除成功
        """
        try:
            result = await self.redis.delete(self._make_key(key))
            return result > 0
        except Exception:
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否存在
        """
        try:
            return await self.redis.exists(self._make_key(key)) > 0
        except Exception:
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """
        设置缓存过期时间
        
        Args:
            key: 缓存键
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否设置成功
        """
        try:
            return await self.redis.expire(self._make_key(key), ttl)
        except Exception:
            return False
    
    async def get_json(self, key: str, default: Any = None) -> Any:
        """
        获取JSON格式的缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            Any: 缓存值
        """
        try:
            value = await self.redis.get(self._make_key(key))
            if value is None:
                return default
            return json.loads(value.decode('utf-8'))
        except Exception:
            return default
    
    async def set_json(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """
        设置JSON格式的缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否设置成功
        """
        try:
            ttl = ttl or settings.CACHE_TTL
            json_value = json.dumps(value, ensure_ascii=False)
            return await self.redis.setex(
                self._make_key(key), 
                ttl, 
                json_value
            )
        except Exception:
            return False
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """
        递增计数器
        
        Args:
            key: 缓存键
            amount: 递增量
            
        Returns:
            int: 递增后的值
        """
        return await self.redis.incrby(self._make_key(key), amount)
    
    async def decrement(self, key: str, amount: int = 1) -> int:
        """
        递减计数器
        
        Args:
            key: 缓存键
            amount: 递减量
            
        Returns:
            int: 递减后的值
        """
        return await self.redis.decrby(self._make_key(key), amount)
    
    async def get_many(self, keys: list[str]) -> dict[str, Any]:
        """
        批量获取缓存值
        
        Args:
            keys: 缓存键列表
            
        Returns:
            dict[str, Any]: 缓存值字典
        """
        try:
            cache_keys = [self._make_key(key) for key in keys]
            values = await self.redis.mget(cache_keys)
            result = {}
            for i, key in enumerate(keys):
                if values[i] is not None:
                    result[key] = pickle.loads(values[i])
                else:
                    result[key] = None
            return result
        except Exception:
            return {key: None for key in keys}
    
    async def set_many(
        self, 
        mapping: dict[str, Any], 
        ttl: Optional[int] = None
    ) -> bool:
        """
        批量设置缓存值
        
        Args:
            mapping: 键值对字典
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否设置成功
        """
        try:
            ttl = ttl or settings.CACHE_TTL
            pipe = self.redis.pipeline()
            for key, value in mapping.items():
                serialized_value = pickle.dumps(value)
                pipe.setex(self._make_key(key), ttl, serialized_value)
            await pipe.execute()
            return True
        except Exception:
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        清除匹配模式的缓存
        
        Args:
            pattern: 匹配模式
            
        Returns:
            int: 删除的键数量
        """
        try:
            keys = await self.redis.keys(self._make_key(pattern))
            if keys:
                return await self.redis.delete(*keys)
            return 0
        except Exception:
            return 0


# 全局缓存管理器实例
cache_manager = CacheManager()
