import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  error_code?: string
  details?: any
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 请求配置
const config: AxiosRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8001/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
}

// 创建axios实例
const client: AxiosInstance = axios.create(config)

// 请求拦截器
client.interceptors.request.use(
  (config) => {
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    // 从localStorage获取token
    const token = localStorage.getItem('auth-token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
client.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 计算请求耗时
    const endTime = new Date()
    const startTime = response.config.metadata?.startTime
    if (startTime) {
      const duration = endTime.getTime() - startTime.getTime()
      console.log(`API请求耗时: ${duration}ms - ${response.config.url}`)
    }

    // 检查业务状态码
    if (response.data && !response.data.success) {
      const errorMessage = response.data.message || '请求失败'
      message.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }

    return response
  },
  async (error) => {
    const { response, config } = error

    // 网络错误
    if (!response) {
      message.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = response

    switch (status) {
      case 401:
        // 未授权，尝试刷新token
        if (!config._retry) {
          config._retry = true
          
          try {
            const refreshToken = localStorage.getItem('refresh-token')
            if (refreshToken) {
              const refreshResponse = await axios.post(
                `${config.baseURL}/auth/refresh`,
                { refresh_token: refreshToken }
              )
              
              const newToken = refreshResponse.data.data.access_token
              localStorage.setItem('auth-token', newToken)
              
              // 重新发送原请求
              config.headers.Authorization = `Bearer ${newToken}`
              return client(config)
            }
          } catch (refreshError) {
            // 刷新失败，清除token并跳转到登录页
            localStorage.removeItem('auth-token')
            localStorage.removeItem('refresh-token')
            window.location.href = '/auth/login'
            return Promise.reject(refreshError)
          }
        }
        
        message.error('登录已过期，请重新登录')
        localStorage.removeItem('auth-token')
        localStorage.removeItem('refresh-token')
        window.location.href = '/auth/login'
        break

      case 403:
        message.error('权限不足，无法访问该资源')
        break

      case 404:
        message.error('请求的资源不存在')
        break

      case 422:
        // 表单验证错误
        if (data?.details) {
          const errors = Object.values(data.details).flat()
          message.error(errors.join(', '))
        } else {
          message.error(data?.message || '请求参数错误')
        }
        break

      case 429:
        message.error('请求过于频繁，请稍后再试')
        break

      case 500:
        message.error('服务器内部错误，请稍后再试')
        break

      case 502:
      case 503:
      case 504:
        message.error('服务暂时不可用，请稍后再试')
        break

      default:
        message.error(data?.message || `请求失败 (${status})`)
    }

    return Promise.reject(error)
  }
)

// API客户端类
export class ApiClient {
  private client: AxiosInstance

  constructor(instance: AxiosInstance) {
    this.client = instance
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get<ApiResponse<T>>(url, config)
    return response.data
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config)
    return response.data
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config)
    return response.data
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config)
    return response.data
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url, config)
    return response.data
  }

  // 上传文件
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // 下载文件
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 设置认证token
  setAuthToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`
    localStorage.setItem('auth-token', token)
  }

  // 清除认证token
  clearAuthToken(): void {
    delete this.client.defaults.headers.common['Authorization']
    localStorage.removeItem('auth-token')
    localStorage.removeItem('refresh-token')
  }

  // 获取原始axios实例
  getInstance(): AxiosInstance {
    return this.client
  }
}

// 导出默认实例
export const apiClient = new ApiClient(client)
export default apiClient
