import React, { useState } from 'react'
import {
  Card,
  Typography,
  Form,
  Switch,
  Select,
  Button,
  Row,
  Col,
  Divider,
  Space,
  Alert,
  message,
  Modal,
  List,
  Tag,
  Tooltip
} from 'antd'
import {
  BellOutlined,
  GlobalOutlined,
  MoonOutlined,
  SunOutlined,
  SecurityScanOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  LogoutOutlined
} from '@ant-design/icons'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { useAuthStore } from '../../stores/authStore'
import { useThemeStore } from '../../stores/themeStore'
import type { UserSettings } from '../../types/auth'

const { Title, Text } = Typography
const { Option } = Select
const { confirm } = Modal

// 时区选项
const TIMEZONE_OPTIONS = [
  { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
  { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
  { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
  { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
  { label: '悉尼时间 (UTC+10)', value: 'Australia/Sydney' }
]

// 语言选项
const LANGUAGE_OPTIONS = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
  { label: '日本語', value: 'ja-JP' },
  { label: '한국어', value: 'ko-KR' }
]

export default function Settings() {
  const { user, logout } = useAuthStore()
  const { theme, setTheme, primaryColor, setPrimaryColor } = useThemeStore()
  const queryClient = useQueryClient()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  // 默认设置
  const defaultSettings: UserSettings = {
    email_notifications: true,
    push_notifications: true,
    alert_notifications: true,
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: 'light'
  }

  // 更新设置
  const updateSettingsMutation = useMutation({
    mutationFn: async (settings: UserSettings) => {
      // 这里应该调用API更新设置
      await new Promise(resolve => setTimeout(resolve, 1000))
      return { data: settings }
    },
    onSuccess: () => {
      message.success('设置保存成功')
    },
    onError: () => {
      message.error('设置保存失败')
    }
  })

  // 处理设置保存
  const handleSaveSettings = async (values: any) => {
    const settings: UserSettings = {
      email_notifications: values.email_notifications,
      push_notifications: values.push_notifications,
      alert_notifications: values.alert_notifications,
      language: values.language,
      timezone: values.timezone,
      theme: values.theme
    }
    updateSettingsMutation.mutate(settings)
  }

  // 导出数据
  const handleExportData = () => {
    confirm({
      title: '导出个人数据',
      icon: <ExclamationCircleOutlined />,
      content: '我们将为您准备包含所有个人数据的文件，这可能需要几分钟时间。',
      okText: '确认导出',
      cancelText: '取消',
      onOk: async () => {
        try {
          message.loading('正在准备导出数据...', 0)
          // 模拟导出过程
          await new Promise(resolve => setTimeout(resolve, 2000))
          message.destroy()
          message.success('数据导出请求已提交，我们将通过邮件发送下载链接')
        } catch (error) {
          message.destroy()
          message.error('导出失败，请重试')
        }
      }
    })
  }

  // 删除账户
  const handleDeleteAccount = () => {
    confirm({
      title: '删除账户',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要删除账户吗？此操作将：</p>
          <ul className="mt-2 space-y-1">
            <li>• 永久删除您的所有数据</li>
            <li>• 删除所有告警设置</li>
            <li>• 无法恢复账户信息</li>
          </ul>
          <p className="mt-2 text-red-500 font-medium">此操作不可逆！</p>
        </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          message.loading('正在删除账户...', 0)
          // 模拟删除过程
          await new Promise(resolve => setTimeout(resolve, 2000))
          message.destroy()
          message.success('账户删除成功')
          logout()
        } catch (error) {
          message.destroy()
          message.error('账户删除失败，请重试')
        }
      }
    })
  }

  // 登出所有设备
  const handleLogoutAllDevices = () => {
    confirm({
      title: '登出所有设备',
      icon: <ExclamationCircleOutlined />,
      content: '这将使您在所有设备上的登录状态失效，您需要重新登录。',
      okText: '确认登出',
      cancelText: '取消',
      onOk: async () => {
        try {
          message.loading('正在登出所有设备...', 0)
          // 模拟登出过程
          await new Promise(resolve => setTimeout(resolve, 1000))
          message.destroy()
          message.success('已登出所有设备')
          logout()
        } catch (error) {
          message.destroy()
          message.error('操作失败，请重试')
        }
      }
    })
  }

  // 主题颜色选项
  const themeColors = [
    { name: '默认蓝', value: '#1890ff' },
    { name: '极光绿', value: '#52c41a' },
    { name: '拂晓蓝', value: '#1677ff' },
    { name: '薄暮红', value: '#f5222d' },
    { name: '火山橙', value: '#fa541c' },
    { name: '日暮黄', value: '#faad14' },
    { name: '极客蓝', value: '#2f54eb' },
    { name: '酱紫色', value: '#722ed1' }
  ]

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="mb-2">
          账户设置
        </Title>
        <Text type="secondary">
          管理您的偏好设置和隐私选项
        </Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 通知设置 */}
        <Col xs={24} lg={12}>
          <Card title={<Space><BellOutlined />通知设置</Space>}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveSettings}
              initialValues={defaultSettings}
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Text strong>邮件通知</Text>
                    <div>
                      <Text type="secondary" className="text-sm">
                        接收重要更新和告警邮件
                      </Text>
                    </div>
                  </div>
                  <Form.Item name="email_notifications" valuePropName="checked" className="mb-0">
                    <Switch />
                  </Form.Item>
                </div>

                <Divider className="my-3" />

                <div className="flex justify-between items-center">
                  <div>
                    <Text strong>推送通知</Text>
                    <div>
                      <Text type="secondary" className="text-sm">
                        浏览器推送通知
                      </Text>
                    </div>
                  </div>
                  <Form.Item name="push_notifications" valuePropName="checked" className="mb-0">
                    <Switch />
                  </Form.Item>
                </div>

                <Divider className="my-3" />

                <div className="flex justify-between items-center">
                  <div>
                    <Text strong>价格告警通知</Text>
                    <div>
                      <Text type="secondary" className="text-sm">
                        USDT价格异常时通知
                      </Text>
                    </div>
                  </div>
                  <Form.Item name="alert_notifications" valuePropName="checked" className="mb-0">
                    <Switch />
                  </Form.Item>
                </div>
              </div>
            </Form>
          </Card>
        </Col>

        {/* 界面设置 */}
        <Col xs={24} lg={12}>
          <Card title={<Space><GlobalOutlined />界面设置</Space>}>
            <div className="space-y-4">
              <div>
                <Text strong className="block mb-2">主题模式</Text>
                <Space>
                  <Button
                    type={theme === 'light' ? 'primary' : 'default'}
                    icon={<SunOutlined />}
                    onClick={() => setTheme('light')}
                  >
                    浅色
                  </Button>
                  <Button
                    type={theme === 'dark' ? 'primary' : 'default'}
                    icon={<MoonOutlined />}
                    onClick={() => setTheme('dark')}
                  >
                    深色
                  </Button>
                  <Button
                    type={theme === 'auto' ? 'primary' : 'default'}
                    onClick={() => setTheme('auto')}
                  >
                    自动
                  </Button>
                </Space>
              </div>

              <Divider className="my-3" />

              <div>
                <Text strong className="block mb-2">主题色彩</Text>
                <div className="grid grid-cols-4 gap-2">
                  {themeColors.map(color => (
                    <Tooltip key={color.value} title={color.name}>
                      <Button
                        className="h-8 w-full"
                        style={{
                          backgroundColor: color.value,
                          borderColor: primaryColor === color.value ? '#000' : color.value
                        }}
                        onClick={() => setPrimaryColor(color.value)}
                      />
                    </Tooltip>
                  ))}
                </div>
              </div>

              <Divider className="my-3" />

              <div>
                <Text strong className="block mb-2">语言</Text>
                <Select
                  defaultValue="zh-CN"
                  style={{ width: '100%' }}
                  options={LANGUAGE_OPTIONS}
                />
              </div>

              <Divider className="my-3" />

              <div>
                <Text strong className="block mb-2">时区</Text>
                <Select
                  defaultValue="Asia/Shanghai"
                  style={{ width: '100%' }}
                  options={TIMEZONE_OPTIONS}
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* 安全设置 */}
        <Col xs={24} lg={12}>
          <Card title={<Space><SecurityScanOutlined />安全设置</Space>}>
            <List
              size="small"
              dataSource={[
                {
                  title: '登出所有设备',
                  description: '使所有设备上的登录状态失效',
                  action: (
                    <Button
                      type="link"
                      icon={<LogoutOutlined />}
                      onClick={handleLogoutAllDevices}
                    >
                      登出
                    </Button>
                  )
                },
                {
                  title: '活跃会话',
                  description: `当前设备 • ${dayjs().format('YYYY-MM-DD HH:mm')}`,
                  action: <Tag color="green">当前</Tag>
                },
                {
                  title: '登录历史',
                  description: '查看最近的登录记录',
                  action: (
                    <Button type="link">
                      查看
                    </Button>
                  )
                }
              ]}
              renderItem={item => (
                <List.Item
                  actions={[item.action]}
                >
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 数据管理 */}
        <Col xs={24} lg={12}>
          <Card title="数据管理">
            <div className="space-y-4">
              <Alert
                message="数据导出"
                description="您可以导出所有个人数据，包括告警设置、使用记录等。"
                type="info"
                showIcon
                action={
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleExportData}
                  >
                    导出数据
                  </Button>
                }
              />

              <Alert
                message="删除账户"
                description="永久删除您的账户和所有相关数据。此操作不可逆。"
                type="warning"
                showIcon
                action={
                  <Button
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleDeleteAccount}
                  >
                    删除账户
                  </Button>
                }
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 保存按钮 */}
      <Card>
        <div className="flex justify-end">
          <Button
            type="primary"
            size="large"
            loading={updateSettingsMutation.isPending}
            onClick={() => form.submit()}
          >
            保存所有设置
          </Button>
        </div>
      </Card>
    </div>
  )
}
