"""
USDT数据相关的Pydantic schemas
"""
import uuid
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class USDTDataBase(BaseModel):
    """USDT数据基础schema"""
    timestamp: datetime
    unix_timestamp: int
    name: str = "Tether"
    symbol: str = "USDT"
    current_price_usd: Decimal = Field(..., decimal_places=10)
    price_precision: Optional[str] = None
    data_source: str
    source_id: Optional[str] = None
    last_updated: Optional[datetime] = None


class USDTDataCreate(USDTDataBase):
    """创建USDT数据schema"""
    market_cap_usd: Optional[int] = None
    market_cap_rank: Optional[int] = None
    total_volume_usd: Optional[int] = None
    circulating_supply: Optional[Decimal] = None
    total_supply: Optional[Decimal] = None
    max_supply: Optional[Decimal] = None
    
    price_change_24h: Optional[Decimal] = None
    price_change_percentage_24h: Optional[float] = None
    price_change_7d: Optional[Decimal] = None
    price_change_percentage_7d: Optional[float] = None
    price_change_30d: Optional[Decimal] = None
    price_change_percentage_30d: Optional[float] = None
    
    market_cap_change_24h: Optional[int] = None
    market_cap_change_percentage_24h: Optional[float] = None
    
    volume_24h: Optional[int] = None
    volume_change_24h: Optional[float] = None
    
    ath: Optional[Decimal] = None
    ath_date: Optional[datetime] = None
    atl: Optional[Decimal] = None
    atl_date: Optional[datetime] = None
    
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_anomaly: bool = False


class USDTData(USDTDataBase):
    """USDT数据schema"""
    id: uuid.UUID
    market_cap_usd: Optional[int]
    market_cap_rank: Optional[int]
    total_volume_usd: Optional[int]
    circulating_supply: Optional[Decimal]
    total_supply: Optional[Decimal]
    max_supply: Optional[Decimal]
    
    price_change_24h: Optional[Decimal]
    price_change_percentage_24h: Optional[float]
    price_change_7d: Optional[Decimal]
    price_change_percentage_7d: Optional[float]
    price_change_30d: Optional[Decimal]
    price_change_percentage_30d: Optional[float]
    
    market_cap_change_24h: Optional[int]
    market_cap_change_percentage_24h: Optional[float]
    
    volume_24h: Optional[int]
    volume_change_24h: Optional[float]
    
    ath: Optional[Decimal]
    ath_date: Optional[datetime]
    atl: Optional[Decimal]
    atl_date: Optional[datetime]
    
    confidence_score: Optional[float]
    is_anomaly: bool
    created_at: datetime
    
    class Config:
        from_attributes = True
    
    @property
    def price_float(self) -> float:
        """获取价格的浮点数表示"""
        return float(self.current_price_usd)
    
    @property
    def is_stable(self) -> bool:
        """判断价格是否稳定（接近1美元）"""
        return abs(float(self.current_price_usd) - 1.0) < 0.01
    
    def calculate_deviation_from_peg(self) -> float:
        """计算与锚定价格(1美元)的偏差百分比"""
        return (float(self.current_price_usd) - 1.0) * 100


class USDTDataResponse(BaseModel):
    """USDT数据响应schema（简化版）"""
    timestamp: datetime
    price: float = Field(..., description="USDT价格")
    price_change_24h: Optional[float] = None
    volume_24h: Optional[int] = None
    market_cap: Optional[int] = None
    
    @validator('price')
    def validate_price(cls, v):
        """验证价格范围"""
        if v <= 0:
            raise ValueError('价格必须大于0')
        return v


class USDTHistoryQuery(BaseModel):
    """USDT历史数据查询schema"""
    start_date: datetime
    end_date: datetime
    interval: str = Field(default="1h", regex="^(1m|5m|15m|1h|4h|1d)$")
    source: Optional[str] = None
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('结束时间必须晚于开始时间')
        return v


class USDTStats(BaseModel):
    """USDT统计数据schema"""
    period: str
    current_price: Decimal
    avg_price: Decimal
    min_price: Decimal
    max_price: Decimal
    price_range: Decimal
    volatility: float
    total_data_points: int
    
    # 价格变化统计
    price_change: Optional[Decimal] = None
    price_change_percentage: Optional[float] = None
    
    # 交易量统计
    avg_volume: Optional[int] = None
    total_volume: Optional[int] = None
    
    # 稳定性指标
    stability_score: float = Field(..., ge=0.0, le=1.0)
    deviation_from_peg: float
    
    # 时间范围
    start_time: datetime
    end_time: datetime
    
    @validator('volatility')
    def validate_volatility(cls, v):
        """验证波动性"""
        if v < 0:
            raise ValueError('波动性不能为负数')
        return v


class USDTAlertBase(BaseModel):
    """USDT告警基础schema"""
    alert_type: str = Field(..., regex="^(price_above|price_below|volatility_high|deviation_high)$")
    threshold_value: Decimal = Field(..., decimal_places=10)
    is_active: bool = True
    description: Optional[str] = Field(None, max_length=500)


class USDTAlertCreate(USDTAlertBase):
    """创建USDT告警schema"""
    
    @validator('threshold_value')
    def validate_threshold(cls, v, values):
        """验证阈值"""
        alert_type = values.get('alert_type')
        if alert_type in ['price_above', 'price_below'] and v <= 0:
            raise ValueError('价格阈值必须大于0')
        elif alert_type == 'volatility_high' and (v < 0 or v > 1):
            raise ValueError('波动性阈值必须在0-1之间')
        elif alert_type == 'deviation_high' and (v < 0 or v > 100):
            raise ValueError('偏差阈值必须在0-100之间')
        return v


class USDTAlert(USDTAlertBase):
    """USDT告警schema"""
    id: uuid.UUID
    user_id: uuid.UUID
    triggered_count: int = 0
    last_triggered: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class USDTExportRequest(BaseModel):
    """USDT数据导出请求schema"""
    start_date: datetime
    end_date: datetime
    format: str = Field(..., regex="^(csv|xlsx|json)$")
    interval: str = Field(default="1h", regex="^(1m|5m|15m|1h|4h|1d)$")
    fields: Optional[List[str]] = None
    include_metadata: bool = True
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('结束时间必须晚于开始时间')
        return v
    
    @validator('fields')
    def validate_fields(cls, v):
        """验证导出字段"""
        if v is not None:
            allowed_fields = [
                'timestamp', 'current_price_usd', 'market_cap_usd', 'volume_24h',
                'price_change_24h', 'price_change_percentage_24h', 'data_source'
            ]
            invalid_fields = [field for field in v if field not in allowed_fields]
            if invalid_fields:
                raise ValueError(f'无效的字段: {", ".join(invalid_fields)}')
        return v


class USDTDataAggregation(BaseModel):
    """USDT数据聚合schema"""
    id: uuid.UUID
    period_start: datetime
    period_end: datetime
    interval_type: str
    
    open_price: Decimal
    close_price: Decimal
    high_price: Decimal
    low_price: Decimal
    avg_price: Decimal
    
    total_volume: Optional[int]
    avg_volume: Optional[int]
    
    volatility: Optional[float]
    price_range: Decimal
    data_points_count: int
    
    created_at: datetime
    
    class Config:
        from_attributes = True


class PriceAlert(BaseModel):
    """价格告警通知schema"""
    alert_id: uuid.UUID
    user_id: uuid.UUID
    alert_type: str
    threshold_value: Decimal
    current_value: Decimal
    message: str
    triggered_at: datetime


class MarketSummary(BaseModel):
    """市场摘要schema"""
    current_price: Decimal
    price_change_24h: Optional[Decimal]
    price_change_percentage_24h: Optional[float]
    market_cap: Optional[int]
    volume_24h: Optional[int]
    last_updated: datetime
    
    # 稳定性指标
    is_stable: bool
    deviation_from_peg: float
    stability_score: float
    
    # 趋势指标
    trend_direction: str = Field(..., regex="^(up|down|stable)$")
    trend_strength: float = Field(..., ge=0.0, le=1.0)


class DataQualityReport(BaseModel):
    """数据质量报告schema"""
    period: str
    total_data_points: int
    valid_data_points: int
    anomaly_count: int
    missing_data_count: int
    
    # 质量指标
    completeness_score: float = Field(..., ge=0.0, le=1.0)
    accuracy_score: float = Field(..., ge=0.0, le=1.0)
    consistency_score: float = Field(..., ge=0.0, le=1.0)
    overall_quality_score: float = Field(..., ge=0.0, le=1.0)
    
    # 数据源统计
    source_distribution: dict
    
    # 时间范围
    start_time: datetime
    end_time: datetime
    generated_at: datetime
