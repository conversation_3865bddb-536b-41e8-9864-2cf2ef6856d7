import { apiClient } from './client'
import type { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest 
} from '../../types/auth'

export class AuthApi {
  // 登录
  async login(credentials: LoginRequest) {
    // 将username转换为email发送给后端
    const loginData = {
      email: credentials.username, // 后端期望email字段
      password: credentials.password,
      remember_me: credentials.remember_me
    }
    return apiClient.post<LoginResponse>('/auth/login', loginData)
  }

  // 注册
  async register(userData: RegisterRequest) {
    return apiClient.post<User>('/auth/register', userData)
  }

  // 刷新token
  async refreshToken(refreshToken: string) {
    return apiClient.post<{ access_token: string }>('/auth/refresh', {
      refresh_token: refreshToken
    })
  }

  // 获取当前用户信息
  async getCurrentUser() {
    return apiClient.get<User>('/auth/me')
  }

  // 登出
  async logout() {
    return apiClient.post('/auth/logout')
  }

  // 修改密码
  async changePassword(data: ChangePasswordRequest) {
    return apiClient.post('/auth/change-password', data)
  }

  // 忘记密码
  async forgotPassword(data: ForgotPasswordRequest) {
    return apiClient.post('/auth/forgot-password', data)
  }

  // 重置密码
  async resetPassword(data: ResetPasswordRequest) {
    return apiClient.post('/auth/reset-password', data)
  }

  // 验证邮箱
  async verifyEmail(token: string) {
    return apiClient.post('/auth/verify-email', { token })
  }

  // 重新发送验证邮件
  async resendVerificationEmail() {
    return apiClient.post('/auth/resend-verification')
  }

  // 设置认证token
  setAuthToken(token: string) {
    apiClient.setAuthToken(token)
  }

  // 清除认证token
  clearAuthToken() {
    apiClient.clearAuthToken()
  }

  // 更新个人资料
  async updateProfile(data: any) {
    return apiClient.put('/auth/profile', data)
  }
}

export const authApi = new AuthApi()
