import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Form, Input, Button, Alert, Space, Typography, Checkbox } from 'antd'
import { UserOutlined, MailOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { useAuthStore } from '../../stores/authStore'
import type { RegisterRequest } from '../../types/auth'

const { Text } = Typography

export default function Register() {
  const navigate = useNavigate()
  const { register } = useAuthStore()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (values: RegisterRequest & { terms_accepted: boolean }) => {
    setLoading(true)
    setError(null)

    try {
      await register({
        email: values.email,
        username: values.username,
        full_name: values.full_name,
        password: values.password,
        confirm_password: values.confirm_password,
        terms_accepted: values.terms_accepted,
      })
      
      setSuccess(true)
      
      // 3秒后跳转到登录页
      setTimeout(() => {
        navigate('/auth/login', {
          state: { message: '注册成功，请登录您的账户' }
        })
      }, 3000)
    } catch (err: any) {
      setError(err.message || '注册失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center space-y-4">
        <Alert
          message="注册成功！"
          description="您的账户已创建成功，即将跳转到登录页面..."
          type="success"
          showIcon
        />
        <Button type="primary" onClick={() => navigate('/auth/login')}>
          立即登录
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          创建账户
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          注册新账户，开始使用USDT监控平台
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* 注册表单 */}
      <Form
        name="register"
        size="large"
        onFinish={handleSubmit}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="email"
          label="邮箱地址"
          rules={[
            { required: true, message: '请输入邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入邮箱地址"
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' },
            { max: 20, message: '用户名最多20个字符' },
            { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入用户名"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="full_name"
          label="姓名"
          rules={[
            { required: true, message: '请输入姓名' },
            { min: 2, message: '姓名至少2个字符' },
            { max: 50, message: '姓名最多50个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入真实姓名"
            autoComplete="name"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 8, message: '密码至少8个字符' },
            { 
              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
              message: '密码必须包含大小写字母和数字' 
            },
          ]}
          hasFeedback
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            autoComplete="new-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item
          name="confirm_password"
          label="确认密码"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('两次输入的密码不一致'))
              },
            }),
          ]}
          hasFeedback
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入密码"
            autoComplete="new-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item
          name="terms_accepted"
          valuePropName="checked"
          rules={[
            { 
              validator: (_, value) =>
                value ? Promise.resolve() : Promise.reject(new Error('请同意服务条款'))
            },
          ]}
        >
          <Checkbox>
            我已阅读并同意{' '}
            <Link to="/terms" target="_blank" className="text-primary">
              服务条款
            </Link>
            {' '}和{' '}
            <Link to="/privacy" target="_blank" className="text-primary">
              隐私政策
            </Link>
          </Checkbox>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            size="large"
          >
            {loading ? '注册中...' : '注册账户'}
          </Button>
        </Form.Item>
      </Form>

      {/* 登录链接 */}
      <div className="text-center">
        <Space>
          <Text type="secondary">已有账户？</Text>
          <Link
            to="/auth/login"
            className="text-primary hover:text-primary-dark font-medium"
          >
            立即登录
          </Link>
        </Space>
      </div>

      {/* 密码要求提示 */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <Text type="secondary" className="text-sm">
          <strong>密码要求：</strong>
        </Text>
        <ul className="mt-1 text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• 至少8个字符</li>
          <li>• 包含大写字母</li>
          <li>• 包含小写字母</li>
          <li>• 包含数字</li>
        </ul>
      </div>
    </div>
  )
}
