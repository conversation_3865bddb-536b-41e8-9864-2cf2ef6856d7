"""
USDT API测试
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.models.user import User as UserModel
from tests.conftest import assert_response_success, assert_response_error


class TestUSDTAPI:
    """USDT API测试类"""
    
    def test_get_current_usdt_data_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_data: list
    ):
        """测试获取当前USDT数据成功"""
        response = client.get("/api/v1/usdt/current", headers=auth_headers)
        data = assert_response_success(response)
        
        assert "current_price_usd" in data["data"]
        assert "timestamp" in data["data"]
        assert "data_source" in data["data"]
        assert data["data"]["symbol"] == "USDT"
    
    def test_get_current_usdt_data_unauthorized(self, client: TestClient):
        """测试获取当前USDT数据 - 未授权"""
        response = client.get("/api/v1/usdt/current")
        assert_response_error(response, 401)
    
    def test_get_usdt_history_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_data: list
    ):
        """测试获取USDT历史数据成功"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(hours=24)
        
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "interval": "1h",
            "page": 1,
            "size": 20
        }
        
        response = client.get("/api/v1/usdt/history", params=params, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "items" in data["data"]
        assert "total" in data["data"]
        assert "page" in data["data"]
        assert "size" in data["data"]
        assert isinstance(data["data"]["items"], list)
    
    def test_get_usdt_history_invalid_date_range(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试获取USDT历史数据 - 无效日期范围"""
        end_date = datetime.utcnow()
        start_date = end_date + timedelta(hours=1)  # 开始时间晚于结束时间
        
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "interval": "1h"
        }
        
        response = client.get("/api/v1/usdt/history", params=params, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_get_usdt_history_large_date_range(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试获取USDT历史数据 - 日期范围过大"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=100)  # 超过90天限制
        
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "interval": "1h"
        }
        
        response = client.get("/api/v1/usdt/history", params=params, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_get_usdt_stats_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_data: list
    ):
        """测试获取USDT统计数据成功"""
        params = {"period": "24h"}
        
        response = client.get("/api/v1/usdt/stats", params=params, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "period" in data["data"]
        assert "current_price" in data["data"]
        assert "avg_price" in data["data"]
        assert "min_price" in data["data"]
        assert "max_price" in data["data"]
        assert "volatility" in data["data"]
        assert "stability_score" in data["data"]
    
    def test_get_usdt_stats_invalid_period(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试获取USDT统计数据 - 无效周期"""
        params = {"period": "invalid_period"}
        
        response = client.get("/api/v1/usdt/stats", params=params, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_get_price_chart_data_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_data: list
    ):
        """测试获取价格图表数据成功"""
        params = {
            "period": "24h",
            "interval": "1h"
        }
        
        response = client.get("/api/v1/usdt/price-chart", params=params, headers=auth_headers)
        data = assert_response_success(response)
        
        assert isinstance(data["data"], list)
        if data["data"]:
            chart_point = data["data"][0]
            assert "timestamp" in chart_point
            assert "price" in chart_point
    
    def test_create_price_alert_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_alert_data: dict
    ):
        """测试创建价格告警成功"""
        response = client.post("/api/v1/usdt/alerts", json=sample_alert_data, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "id" in data["data"]
        assert data["data"]["alert_type"] == sample_alert_data["alert_type"]
        assert str(data["data"]["threshold_value"]) == sample_alert_data["threshold_value"]
        assert data["data"]["is_active"] == sample_alert_data["is_active"]
    
    def test_create_price_alert_invalid_threshold(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试创建价格告警 - 无效阈值"""
        alert_data = {
            "alert_type": "price_above",
            "threshold_value": "-1.0",  # 负数阈值
            "is_active": True,
            "description": "Invalid threshold test"
        }
        
        response = client.post("/api/v1/usdt/alerts", json=alert_data, headers=auth_headers)
        assert_response_error(response, 422)  # Validation error
    
    def test_get_user_alerts_success(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试获取用户告警列表成功"""
        params = {
            "page": 1,
            "size": 20,
            "is_active": True
        }
        
        response = client.get("/api/v1/usdt/alerts", params=params, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "items" in data["data"]
        assert "total" in data["data"]
        assert "page" in data["data"]
        assert "size" in data["data"]
    
    def test_export_usdt_data_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_export_request: dict
    ):
        """测试导出USDT数据成功"""
        response = client.post("/api/v1/usdt/export", json=sample_usdt_export_request, headers=auth_headers)
        data = assert_response_success(response)
        
        assert "export_id" in data["data"]
        assert "message" in data["data"]
    
    def test_export_usdt_data_invalid_date_range(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试导出USDT数据 - 无效日期范围"""
        end_date = datetime.utcnow()
        start_date = end_date + timedelta(hours=1)  # 开始时间晚于结束时间
        
        export_request = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "format": "csv",
            "interval": "1h"
        }
        
        response = client.post("/api/v1/usdt/export", json=export_request, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_export_usdt_data_large_date_range(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试导出USDT数据 - 日期范围过大"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=35)  # 超过30天限制
        
        export_request = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "format": "csv",
            "interval": "1h"
        }
        
        response = client.post("/api/v1/usdt/export", json=export_request, headers=auth_headers)
        assert_response_error(response, 400)
    
    def test_get_export_status_success(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        sample_usdt_export_request: dict
    ):
        """测试获取导出状态成功"""
        # 首先创建导出任务
        export_response = client.post("/api/v1/usdt/export", json=sample_usdt_export_request, headers=auth_headers)
        export_data = assert_response_success(export_response)
        export_id = export_data["data"]["export_id"]
        
        # 获取导出状态
        response = client.get(f"/api/v1/usdt/export/{export_id}/status", headers=auth_headers)
        data = assert_response_success(response)
        
        assert "export_id" in data["data"]
        assert "status" in data["data"]
        assert "progress" in data["data"]
    
    def test_get_export_status_not_found(
        self, 
        client: TestClient, 
        auth_headers: dict
    ):
        """测试获取导出状态 - 任务不存在"""
        fake_export_id = "00000000-0000-0000-0000-000000000000"
        
        response = client.get(f"/api/v1/usdt/export/{fake_export_id}/status", headers=auth_headers)
        assert_response_error(response, 404)
    
    @pytest.mark.parametrize("interval", ["1m", "5m", "15m", "1h", "4h", "1d"])
    def test_get_usdt_history_valid_intervals(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        interval: str,
        sample_usdt_data: list
    ):
        """测试获取USDT历史数据 - 有效时间间隔"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(hours=24)
        
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "interval": interval
        }
        
        response = client.get("/api/v1/usdt/history", params=params, headers=auth_headers)
        assert_response_success(response)
    
    @pytest.mark.parametrize("period", ["1h", "24h", "7d", "30d"])
    def test_get_usdt_stats_valid_periods(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        period: str,
        sample_usdt_data: list
    ):
        """测试获取USDT统计数据 - 有效时间周期"""
        params = {"period": period}
        
        response = client.get("/api/v1/usdt/stats", params=params, headers=auth_headers)
        assert_response_success(response)
    
    @pytest.mark.parametrize("export_format", ["csv", "xlsx", "json"])
    def test_export_usdt_data_valid_formats(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        export_format: str
    ):
        """测试导出USDT数据 - 有效格式"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(hours=24)
        
        export_request = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "format": export_format,
            "interval": "1h"
        }
        
        response = client.post("/api/v1/usdt/export", json=export_request, headers=auth_headers)
        assert_response_success(response)
    
    @pytest.mark.parametrize("alert_type", ["price_above", "price_below", "volatility_high", "deviation_high"])
    def test_create_price_alert_valid_types(
        self, 
        client: TestClient, 
        auth_headers: dict, 
        alert_type: str
    ):
        """测试创建价格告警 - 有效告警类型"""
        threshold_map = {
            "price_above": "1.01",
            "price_below": "0.99",
            "volatility_high": "0.1",
            "deviation_high": "5.0"
        }
        
        alert_data = {
            "alert_type": alert_type,
            "threshold_value": threshold_map[alert_type],
            "is_active": True,
            "description": f"Test {alert_type} alert"
        }
        
        response = client.post("/api/v1/usdt/alerts", json=alert_data, headers=auth_headers)
        assert_response_success(response)
