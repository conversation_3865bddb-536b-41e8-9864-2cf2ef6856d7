"""
系统服务
"""
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.cache import cache_manager
from app.core.database import engine
from app.core.exceptions import AppException
from app.domain.schemas.common import CacheStats, DatabaseStats, SystemStats


class SystemService:
    """系统服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                "project_name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "debug_mode": settings.DEBUG,
                "python_version": self._get_python_version(),
                "server_time": datetime.utcnow().isoformat(),
                "uptime": self._get_uptime(),
                "timezone": "UTC"
            }
            
        except Exception as e:
            raise AppException(
                error_code="SYSTEM_INFO_ERROR",
                message="获取系统信息失败",
                details={"error": str(e)}
            )
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            # 获取各种统计信息
            cache_stats = await self.get_cache_stats()
            db_stats = await self.get_database_stats()
            perf_stats = await self.get_performance_stats()
            
            return {
                "cache": cache_stats.dict(),
                "database": db_stats.dict(),
                "performance": perf_stats.dict(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            raise AppException(
                error_code="SYSTEM_STATS_ERROR",
                message="获取系统统计失败",
                details={"error": str(e)}
            )
    
    async def get_cache_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        try:
            # 获取Redis统计信息
            redis_info = await cache_manager.redis.info()
            
            # 计算命中率
            hits = int(redis_info.get('keyspace_hits', 0))
            misses = int(redis_info.get('keyspace_misses', 0))
            total_requests = hits + misses
            
            hit_rate = hits / total_requests if total_requests > 0 else 0
            miss_rate = misses / total_requests if total_requests > 0 else 0
            
            return CacheStats(
                hit_rate=hit_rate,
                miss_rate=miss_rate,
                total_keys=int(redis_info.get('db0', {}).get('keys', 0)),
                memory_usage=int(redis_info.get('used_memory', 0)),
                connections=int(redis_info.get('connected_clients', 0))
            )
            
        except Exception as e:
            # 返回默认值
            return CacheStats(
                hit_rate=0.0,
                miss_rate=0.0,
                total_keys=0,
                memory_usage=0,
                connections=0
            )
    
    async def clear_cache(self, pattern: str = "*") -> int:
        """清除缓存"""
        try:
            # 获取匹配的键
            keys = await cache_manager.redis.keys(pattern)
            
            if keys:
                # 删除键
                deleted_count = await cache_manager.redis.delete(*keys)
                return deleted_count
            
            return 0
            
        except Exception as e:
            raise AppException(
                error_code="CACHE_CLEAR_ERROR",
                message="清除缓存失败",
                details={"error": str(e)}
            )
    
    async def get_database_stats(self) -> DatabaseStats:
        """获取数据库统计信息"""
        try:
            # 获取连接池信息
            pool = engine.pool
            
            return DatabaseStats(
                total_connections=pool.size(),
                active_connections=pool.checkedout(),
                idle_connections=pool.checkedin(),
                total_queries=0,  # 需要从数据库获取
                slow_queries=0,   # 需要从数据库获取
                database_size=0   # 需要从数据库获取
            )
            
        except Exception as e:
            # 返回默认值
            return DatabaseStats(
                total_connections=0,
                active_connections=0,
                idle_connections=0,
                total_queries=0,
                slow_queries=0,
                database_size=0
            )
    
    async def get_performance_stats(self) -> SystemStats:
        """获取性能统计"""
        try:
            # 获取系统性能指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            return SystemStats(
                cpu_usage=cpu_percent,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                network_io={
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                uptime=int(time.time() - psutil.boot_time())
            )
            
        except Exception as e:
            # 返回默认值
            return SystemStats(
                cpu_usage=0.0,
                memory_usage=0.0,
                disk_usage=0.0,
                network_io={},
                uptime=0
            )
    
    async def get_system_logs(self, level: str = "INFO", limit: int = 100) -> Dict[str, Any]:
        """获取系统日志"""
        try:
            # TODO: 实现日志读取逻辑
            # 这里应该从日志文件或日志系统中读取日志
            
            logs = [
                {
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "INFO",
                    "message": "System is running normally",
                    "module": "system"
                }
            ]
            
            return {
                "logs": logs,
                "total": len(logs),
                "level": level,
                "limit": limit
            }
            
        except Exception as e:
            raise AppException(
                error_code="SYSTEM_LOGS_ERROR",
                message="获取系统日志失败",
                details={"error": str(e)}
            )
    
    async def set_maintenance_mode(self, enabled: bool, message: str = "系统维护中") -> None:
        """设置维护模式"""
        try:
            if enabled:
                await cache_manager.set("system:maintenance_mode", True)
                await cache_manager.set("system:maintenance_message", message)
            else:
                await cache_manager.delete("system:maintenance_mode")
                await cache_manager.delete("system:maintenance_message")
                
        except Exception as e:
            raise AppException(
                error_code="MAINTENANCE_MODE_ERROR",
                message="设置维护模式失败",
                details={"error": str(e)}
            )
    
    async def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        try:
            # 返回非敏感的配置信息
            return {
                "project_name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "debug": settings.DEBUG,
                "monitoring_interval": settings.MONITORING_INTERVAL,
                "alert_threshold": settings.ALERT_THRESHOLD,
                "data_retention_days": settings.DATA_RETENTION_DAYS,
                "cache_ttl": settings.CACHE_TTL,
                "rate_limit_per_minute": settings.RATE_LIMIT_PER_MINUTE,
                "default_page_size": settings.DEFAULT_PAGE_SIZE,
                "max_page_size": settings.MAX_PAGE_SIZE
            }
            
        except Exception as e:
            raise AppException(
                error_code="SYSTEM_CONFIG_ERROR",
                message="获取系统配置失败",
                details={"error": str(e)}
            )
    
    async def update_system_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新系统配置"""
        try:
            # 验证配置数据
            allowed_configs = {
                "monitoring_interval", "alert_threshold", "data_retention_days",
                "cache_ttl", "rate_limit_per_minute", "default_page_size", "max_page_size"
            }
            
            invalid_configs = set(config_data.keys()) - allowed_configs
            if invalid_configs:
                raise AppException(
                    error_code="INVALID_CONFIG_KEYS",
                    message=f"无效的配置项: {', '.join(invalid_configs)}"
                )
            
            # TODO: 实现配置更新逻辑
            # 这里应该将配置保存到数据库或配置文件
            
            # 返回更新后的配置
            updated_config = await self.get_system_config()
            updated_config.update(config_data)
            
            return updated_config
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="SYSTEM_CONFIG_UPDATE_ERROR",
                message="更新系统配置失败",
                details={"error": str(e)}
            )
    
    async def create_backup(self) -> Dict[str, Any]:
        """创建系统备份"""
        try:
            backup_id = f"backup_{int(time.time())}"
            backup_time = datetime.utcnow()
            
            # TODO: 实现备份逻辑
            # 这里应该备份数据库和重要文件
            
            backup_info = {
                "backup_id": backup_id,
                "created_at": backup_time.isoformat(),
                "status": "completed",
                "size": "0 MB",  # 实际大小
                "type": "full",
                "description": "系统完整备份"
            }
            
            return backup_info
            
        except Exception as e:
            raise AppException(
                error_code="BACKUP_CREATE_ERROR",
                message="创建备份失败",
                details={"error": str(e)}
            )
    
    async def list_backups(self) -> Dict[str, Any]:
        """获取备份列表"""
        try:
            # TODO: 实现备份列表查询
            # 这里应该从备份存储中获取备份列表
            
            backups = [
                {
                    "backup_id": "backup_1234567890",
                    "created_at": datetime.utcnow().isoformat(),
                    "status": "completed",
                    "size": "10 MB",
                    "type": "full",
                    "description": "系统完整备份"
                }
            ]
            
            return {
                "backups": backups,
                "total": len(backups)
            }
            
        except Exception as e:
            raise AppException(
                error_code="BACKUP_LIST_ERROR",
                message="获取备份列表失败",
                details={"error": str(e)}
            )
    
    def _get_python_version(self) -> str:
        """获取Python版本"""
        import sys
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    def _get_uptime(self) -> int:
        """获取系统运行时间（秒）"""
        try:
            return int(time.time() - psutil.boot_time())
        except Exception:
            return 0
