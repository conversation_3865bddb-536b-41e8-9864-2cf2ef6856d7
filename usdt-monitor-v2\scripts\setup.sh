#!/bin/bash

# USDT监控平台 V2 快速设置脚本
# 此脚本将帮助您快速设置开发环境

set -e

echo "🚀 USDT监控平台 V2 快速设置"
echo "================================"

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装。请先安装 Docker。"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装。请先安装 Docker Compose。"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装。请先安装 Node.js 18+。"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 未安装。请先安装 Python 3.11+。"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# 创建环境配置文件
setup_env() {
    echo "⚙️ 设置环境配置..."
    
    # 后端环境配置
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        echo "✅ 创建后端环境配置文件"
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -base64 32)
        sed -i "s/your-super-secret-key-change-in-production/$SECRET_KEY/g" backend/.env
        echo "✅ 生成安全密钥"
    else
        echo "⚠️ 后端环境配置文件已存在，跳过创建"
    fi
    
    # 前端环境配置
    if [ ! -f "frontend/.env.local" ]; then
        cat > frontend/.env.local << EOF
VITE_API_URL=http://localhost:8000/api/v1
VITE_WS_URL=http://localhost:8000
VITE_APP_NAME=USDT监控平台
VITE_APP_VERSION=2.0.0
EOF
        echo "✅ 创建前端环境配置文件"
    else
        echo "⚠️ 前端环境配置文件已存在，跳过创建"
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装依赖..."
    
    # 后端依赖
    echo "📦 安装后端依赖..."
    cd backend
    if command -v poetry &> /dev/null; then
        poetry install
    else
        pip install -r requirements.txt
    fi
    cd ..
    
    # 前端依赖
    echo "📦 安装前端依赖..."
    cd frontend
    npm install
    cd ..
    
    echo "✅ 依赖安装完成"
}

# 启动服务
start_services() {
    echo "🐳 启动Docker服务..."
    
    # 启动基础服务（数据库、缓存）
    docker-compose up -d postgres redis
    
    # 等待服务启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    echo "🗄️ 运行数据库迁移..."
    cd backend
    if command -v poetry &> /dev/null; then
        poetry run alembic upgrade head
    else
        alembic upgrade head
    fi
    cd ..
    
    echo "✅ 基础服务启动完成"
}

# 创建初始数据
create_initial_data() {
    echo "👤 创建初始数据..."
    
    cd backend
    if command -v poetry &> /dev/null; then
        poetry run python -m app.infrastructure.init_data
    else
        python -m app.infrastructure.init_data
    fi
    cd ..
    
    echo "✅ 初始数据创建完成"
}

# 启动开发服务器
start_dev_servers() {
    echo "🚀 启动开发服务器..."
    
    # 启动后端服务器
    echo "🔧 启动后端服务器..."
    cd backend
    if command -v poetry &> /dev/null; then
        poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    else
        uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    fi
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 5
    
    # 启动前端服务器
    echo "🎨 启动前端服务器..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo "✅ 开发服务器启动完成"
    echo ""
    echo "🎉 设置完成！"
    echo "================================"
    echo "📱 前端应用: http://localhost:3000"
    echo "🔧 后端API: http://localhost:8000"
    echo "📖 API文档: http://localhost:8000/api/v1/docs"
    echo "🗄️ 数据库: localhost:5432"
    echo "🔴 Redis: localhost:6379"
    echo ""
    echo "👤 默认管理员账户:"
    echo "   邮箱: <EMAIL>"
    echo "   密码: admin123456"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    
    # 等待用户中断
    trap "echo ''; echo '🛑 停止服务器...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose down; echo '✅ 服务器已停止'; exit 0" INT
    wait
}

# 显示帮助信息
show_help() {
    echo "USDT监控平台 V2 快速设置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h     显示此帮助信息"
    echo "  --env-only     仅设置环境配置"
    echo "  --deps-only    仅安装依赖"
    echo "  --docker-only  仅启动Docker服务"
    echo "  --dev          启动开发模式（默认）"
    echo ""
    echo "示例:"
    echo "  $0              # 完整设置并启动开发服务器"
    echo "  $0 --env-only   # 仅创建环境配置文件"
    echo "  $0 --deps-only  # 仅安装依赖"
}

# 主函数
main() {
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --env-only)
            setup_env
            ;;
        --deps-only)
            install_dependencies
            ;;
        --docker-only)
            start_services
            ;;
        --dev|"")
            check_requirements
            setup_env
            install_dependencies
            start_services
            create_initial_data
            start_dev_servers
            ;;
        *)
            echo "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
