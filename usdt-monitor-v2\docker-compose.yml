version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: usdt_monitor_postgres
    environment:
      POSTGRES_USER: usdt_monitor
      POSTGRES_PASSWORD: usdt_monitor_password
      POSTGRES_DB: usdt_monitor
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U usdt_monitor"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - usdt_monitor_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: usdt_monitor_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass usdt_monitor_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - usdt_monitor_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: usdt_monitor_backend
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=usdt_monitor
      - POSTGRES_PASSWORD=usdt_monitor_password
      - POSTGRES_DB=usdt_monitor
      - REDIS_HOST=redis
      - REDIS_PASSWORD=usdt_monitor_redis_password
      - SECRET_KEY=development-secret-key-change-in-production
      - FIRST_SUPERUSER_EMAIL=<EMAIL>
      - FIRST_SUPERUSER_PASSWORD=admin123456
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: usdt_monitor_frontend
    environment:
      - VITE_API_URL=http://localhost:8000/api/v1
      - VITE_WS_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: usdt_monitor_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # 数据收集器
  data_collector:
    build:
      context: ./data-collector
      dockerfile: Dockerfile
    container_name: usdt_monitor_collector
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=usdt_monitor
      - POSTGRES_PASSWORD=usdt_monitor_password
      - POSTGRES_DB=usdt_monitor
      - REDIS_HOST=redis
      - REDIS_PASSWORD=usdt_monitor_redis_password
      - COINGECKO_API_KEY=${COINGECKO_API_KEY}
      - MONITORING_INTERVAL=30
    volumes:
      - ./data-collector:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: usdt_monitor_celery_worker
    command: celery -A app.infrastructure.celery worker --loglevel=info
    environment:
      - ENVIRONMENT=development
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=usdt_monitor
      - POSTGRES_PASSWORD=usdt_monitor_password
      - POSTGRES_DB=usdt_monitor
      - REDIS_HOST=redis
      - REDIS_PASSWORD=usdt_monitor_redis_password
      - CELERY_BROKER_URL=redis://:usdt_monitor_redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:usdt_monitor_redis_password@redis:6379/2
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # Celery Beat (定时任务)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: usdt_monitor_celery_beat
    command: celery -A app.infrastructure.celery beat --loglevel=info
    environment:
      - ENVIRONMENT=development
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=usdt_monitor
      - POSTGRES_PASSWORD=usdt_monitor_password
      - POSTGRES_DB=usdt_monitor
      - REDIS_HOST=redis
      - REDIS_PASSWORD=usdt_monitor_redis_password
      - CELERY_BROKER_URL=redis://:usdt_monitor_redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:usdt_monitor_redis_password@redis:6379/2
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: usdt_monitor_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - usdt_monitor_network
    restart: unless-stopped

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: usdt_monitor_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123456
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - usdt_monitor_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  backend_uploads:
  nginx_logs:
  prometheus_data:
  grafana_data:

networks:
  usdt_monitor_network:
    driver: bridge
