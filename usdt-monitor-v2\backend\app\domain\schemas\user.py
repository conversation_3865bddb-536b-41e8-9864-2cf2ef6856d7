"""
用户相关的Pydantic schemas
"""
import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, EmailStr, Field, validator


class PermissionBase(BaseModel):
    """权限基础schema"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    resource: str = Field(..., min_length=1, max_length=50)
    action: str = Field(..., min_length=1, max_length=50)


class PermissionCreate(PermissionBase):
    """创建权限schema"""
    pass


class Permission(PermissionBase):
    """权限schema"""
    id: uuid.UUID
    created_at: datetime
    
    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    """角色基础schema"""
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = True


class RoleCreate(RoleBase):
    """创建角色schema"""
    permission_ids: List[uuid.UUID] = Field(default_factory=list)


class RoleUpdate(BaseModel):
    """更新角色schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    permission_ids: Optional[List[uuid.UUID]] = None


class Role(RoleBase):
    """角色schema"""
    id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime]
    permissions: List[Permission] = Field(default_factory=list)
    
    class Config:
        from_attributes = True


class UserBase(BaseModel):
    """用户基础schema"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    is_active: bool = True
    is_verified: bool = False
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v.lower()
    
    @validator('phone')
    def validate_phone(cls, v):
        """验证手机号格式"""
        if v is not None:
            import re
            if not re.match(r'^[+]?[\d\s\-\(\)]+$', v):
                raise ValueError('手机号格式不正确')
        return v


class UserCreate(UserBase):
    """创建用户schema"""
    password: str = Field(..., min_length=8, max_length=128)
    role_ids: List[uuid.UUID] = Field(default_factory=list)
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        from app.core.security import validate_password_strength
        
        result = validate_password_strength(v)
        if not result['is_valid']:
            raise ValueError('; '.join(result['errors']))
        return v


class UserUpdate(BaseModel):
    """更新用户schema"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    avatar_url: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    role_ids: Optional[List[uuid.UUID]] = None
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if v is not None:
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('用户名只能包含字母、数字、下划线和连字符')
            return v.lower()
        return v


class UserPasswordUpdate(BaseModel):
    """用户密码更新schema"""
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码确认不匹配')
        return v
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """验证新密码强度"""
        from app.core.security import validate_password_strength
        
        result = validate_password_strength(v)
        if not result['is_valid']:
            raise ValueError('; '.join(result['errors']))
        return v


class User(UserBase):
    """用户schema"""
    id: uuid.UUID
    avatar_url: Optional[str]
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]
    failed_login_attempts: int
    locked_until: Optional[datetime]
    password_changed_at: Optional[datetime]
    roles: List[Role] = Field(default_factory=list)
    
    class Config:
        from_attributes = True
    
    @property
    def is_locked(self) -> bool:
        """检查用户是否被锁定"""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def has_permission(self, permission_name: str) -> bool:
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        
        for role in self.roles:
            for permission in role.permissions:
                if permission.name == permission_name:
                    return True
        return False
    
    def has_role(self, role_name: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.name == role_name for role in self.roles)


class UserPublic(BaseModel):
    """用户公开信息schema"""
    id: uuid.UUID
    username: str
    full_name: Optional[str]
    avatar_url: Optional[str]
    is_active: bool
    is_verified: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserActivityBase(BaseModel):
    """用户活动基础schema"""
    action: str = Field(..., min_length=1, max_length=100)
    resource: Optional[str] = Field(None, max_length=100)
    details: Optional[str] = Field(None, max_length=1000)
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = Field(None, max_length=500)


class UserActivityCreate(UserActivityBase):
    """创建用户活动schema"""
    user_id: uuid.UUID


class UserActivity(UserActivityBase):
    """用户活动schema"""
    id: uuid.UUID
    user_id: uuid.UUID
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserStats(BaseModel):
    """用户统计schema"""
    total_users: int
    active_users: int
    verified_users: int
    locked_users: int
    new_users_today: int
    new_users_this_week: int
    new_users_this_month: int


class UserProfile(BaseModel):
    """用户个人资料更新schema"""
    full_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    avatar_url: Optional[str] = Field(None, max_length=500)
    bio: Optional[str] = Field(None, max_length=500)
    timezone: Optional[str] = Field(None, max_length=50)

    @validator('phone')
    def validate_phone(cls, v):
        """验证手机号格式"""
        if v is not None:
            import re
            if not re.match(r'^[+]?[\d\s\-\(\)]+$', v):
                raise ValueError('手机号格式不正确')
        return v


class UserListResponse(BaseModel):
    """用户列表响应schema"""
    users: List[User]
    total: int
    page: int
    page_size: int
    total_pages: int
