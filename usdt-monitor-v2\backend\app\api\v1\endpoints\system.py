"""
系统管理API端点
"""
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status

from app.api.deps import get_current_active_user, get_current_admin_user
from app.domain.schemas.common import ApiResponse, HealthCheck, CacheStats, DatabaseStats, SystemStats
from app.domain.schemas.user import User
from app.application.services.system_service import SystemService

router = APIRouter()


@router.get("/health", response_model=ApiResponse[HealthCheck])
async def get_system_health() -> Any:
    """
    获取系统健康状态（公开端点）
    """
    try:
        from app.core.config import settings
        import time
        
        health_data = HealthCheck(
            status="healthy",
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            timestamp=time.time()
        )
        
        return ApiResponse(
            data=health_data,
            message="系统运行正常"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="系统健康检查失败"
        )


@router.get("/info", response_model=ApiResponse[Dict[str, Any]])
async def get_system_info(
    current_user: User = Depends(get_current_active_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取系统信息
    """
    try:
        system_info = await system_service.get_system_info()
        
        return ApiResponse(
            data=system_info,
            message="获取系统信息成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统信息失败"
        )


@router.get("/stats", response_model=ApiResponse[Dict[str, Any]])
async def get_system_stats(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取系统统计信息（管理员权限）
    """
    try:
        stats = await system_service.get_system_stats()
        
        return ApiResponse(
            data=stats,
            message="获取系统统计成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统统计失败"
        )


@router.get("/cache/stats", response_model=ApiResponse[CacheStats])
async def get_cache_stats(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取缓存统计信息（管理员权限）
    """
    try:
        cache_stats = await system_service.get_cache_stats()
        
        return ApiResponse(
            data=cache_stats,
            message="获取缓存统计成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取缓存统计失败"
        )


@router.post("/cache/clear", response_model=ApiResponse[dict])
async def clear_cache(
    pattern: str = "*",
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    清除缓存（管理员权限）
    """
    try:
        cleared_count = await system_service.clear_cache(pattern)
        
        return ApiResponse(
            data={
                "cleared_count": cleared_count,
                "pattern": pattern
            },
            message=f"成功清除 {cleared_count} 个缓存项"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清除缓存失败"
        )


@router.get("/database/stats", response_model=ApiResponse[DatabaseStats])
async def get_database_stats(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取数据库统计信息（管理员权限）
    """
    try:
        db_stats = await system_service.get_database_stats()
        
        return ApiResponse(
            data=db_stats,
            message="获取数据库统计成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取数据库统计失败"
        )


@router.get("/performance", response_model=ApiResponse[SystemStats])
async def get_performance_stats(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取系统性能统计（管理员权限）
    """
    try:
        perf_stats = await system_service.get_performance_stats()
        
        return ApiResponse(
            data=perf_stats,
            message="获取性能统计成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能统计失败"
        )


@router.get("/logs", response_model=ApiResponse[Dict[str, Any]])
async def get_system_logs(
    level: str = "INFO",
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取系统日志（管理员权限）
    """
    try:
        if limit > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="日志条数不能超过1000条"
            )
        
        logs = await system_service.get_system_logs(level, limit)
        
        return ApiResponse(
            data=logs,
            message="获取系统日志成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统日志失败"
        )


@router.post("/maintenance", response_model=ApiResponse[dict])
async def toggle_maintenance_mode(
    enabled: bool,
    message: str = "系统维护中，请稍后再试",
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    切换维护模式（管理员权限）
    """
    try:
        await system_service.set_maintenance_mode(enabled, message)
        
        status_text = "启用" if enabled else "禁用"
        return ApiResponse(
            data={
                "maintenance_enabled": enabled,
                "message": message
            },
            message=f"维护模式已{status_text}"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换维护模式失败"
        )


@router.get("/config", response_model=ApiResponse[Dict[str, Any]])
async def get_system_config(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取系统配置（管理员权限）
    """
    try:
        config = await system_service.get_system_config()
        
        return ApiResponse(
            data=config,
            message="获取系统配置成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统配置失败"
        )


@router.put("/config", response_model=ApiResponse[Dict[str, Any]])
async def update_system_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    更新系统配置（管理员权限）
    """
    try:
        updated_config = await system_service.update_system_config(config_data)
        
        return ApiResponse(
            data=updated_config,
            message="系统配置更新成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新系统配置失败"
        )


@router.post("/backup", response_model=ApiResponse[dict])
async def create_system_backup(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    创建系统备份（管理员权限）
    """
    try:
        backup_info = await system_service.create_backup()
        
        return ApiResponse(
            data=backup_info,
            message="系统备份创建成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建系统备份失败"
        )


@router.get("/backups", response_model=ApiResponse[Dict[str, Any]])
async def list_system_backups(
    current_user: User = Depends(get_current_admin_user),
    system_service: SystemService = Depends()
) -> Any:
    """
    获取备份列表（管理员权限）
    """
    try:
        backups = await system_service.list_backups()
        
        return ApiResponse(
            data=backups,
            message="获取备份列表成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取备份列表失败"
        )
