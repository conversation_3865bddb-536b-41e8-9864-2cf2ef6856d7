# 应用配置
PROJECT_NAME=USDT监控平台
VERSION=2.0.0
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000
WORKERS=1

# 安全配置
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=usdt_monitor
POSTGRES_PASSWORD=your-database-password
POSTGRES_DB=usdt_monitor
POSTGRES_PORT=5432

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=USDT监控平台

# 超级用户配置
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123456

# 外部API配置
COINGECKO_API_KEY=your-coingecko-api-key
COINBASE_API_KEY=your-coinbase-api-key
BINANCE_API_KEY=your-binance-api-key

# 监控配置
MONITORING_INTERVAL=30
ALERT_THRESHOLD=0.1
DATA_RETENTION_DAYS=90

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=

# 缓存配置
CACHE_TTL=300
CACHE_PREFIX=usdt_monitor

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# 文件上传配置
MAX_UPLOAD_SIZE=10485760
UPLOAD_DIR=uploads

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Sentry配置
SENTRY_DSN=

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
