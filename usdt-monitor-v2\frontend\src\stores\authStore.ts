import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authApi } from '../services/api/auth'
import type { User, LoginRequest, RegisterRequest } from '../types/auth'

interface AuthState {
  // 状态
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  isInitialized: boolean
  
  // 操作
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  refreshAuth: () => Promise<void>
  updateUser: (user: Partial<User>) => void
  initializeAuth: () => void
  
  // 权限检查
  hasPermission: (permission: string) => boolean
  isAdmin: () => boolean
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      isInitialized: false,

      // 登录
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true })
        try {
          const response = await authApi.login(credentials)
          const { user, access_token, refresh_token } = response.data
          
          set({
            user,
            token: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            isLoading: false,
          })
          
          // 设置axios默认header
          authApi.setAuthToken(access_token)
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      // 注册
      register: async (userData: RegisterRequest) => {
        set({ isLoading: true })
        try {
          await authApi.register(userData)
          set({ isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      // 登出
      logout: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
        })
        
        // 清除axios默认header
        authApi.clearAuthToken()
        
        // 清除本地存储
        localStorage.removeItem('auth-storage')
      },

      // 刷新认证
      refreshAuth: async () => {
        const { refreshToken } = get()
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }

        try {
          const response = await authApi.refreshToken(refreshToken)
          const { access_token } = response.data
          
          set({ token: access_token })
          authApi.setAuthToken(access_token)
        } catch (error) {
          // 刷新失败，清除认证状态
          get().logout()
          throw error
        }
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        }))
      },

      // 初始化认证状态
      initializeAuth: () => {
        const { token, user } = get()
        
        if (token && user) {
          authApi.setAuthToken(token)
          set({ isAuthenticated: true })
        }
        
        set({ isInitialized: true })
      },

      // 权限检查
      hasPermission: (permission: string) => {
        const { user } = get()
        if (!user) return false
        
        // 超级管理员拥有所有权限
        if (user.is_superuser) return true
        
        // 检查用户角色权限
        return user.permissions?.includes(permission) || false
      },

      // 检查是否为管理员
      isAdmin: () => {
        const { user } = get()
        return user?.is_superuser || false
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
