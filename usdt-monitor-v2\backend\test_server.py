from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import random
import time
from datetime import datetime, timedelta

app = FastAPI(title="USDT Monitor API", version="2.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class LoginRequest(BaseModel):
    email: str
    password: str

class User(BaseModel):
    id: int
    email: str
    full_name: str
    is_active: bool
    is_admin: bool
    created_at: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: User

# 生成模拟数据
def generate_mock_price():
    return round(random.uniform(0.998, 1.002), 4)

def generate_mock_history(hours: int = 24):
    data = []
    now = datetime.now()
    for i in range(hours):
        timestamp = now - timedelta(hours=hours-i)
        price = generate_mock_price()
        data.append({
            "id": i + 1,
            "price": price,
            "source": "mock",
            "timestamp": timestamp.isoformat(),
            "volume_24h": random.uniform(1000000, 5000000),
            "market_cap": random.uniform(80000000000, 90000000000)
        })
    return data

@app.get("/")
def read_root():
    return {"message": "USDT Monitor API v2.0.0", "status": "running"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "timestamp": time.time()}

# 认证API
@app.post("/api/v1/auth/login", response_model=LoginResponse)
def login(request: LoginRequest):
    # 简单的模拟登录验证
    if request.email == "<EMAIL>" and request.password == "admin123":
        user = User(
            id=1,
            email="<EMAIL>",
            full_name="管理员",
            is_active=True,
            is_admin=True,
            created_at=datetime.now().isoformat()
        )
        return LoginResponse(
            access_token="mock-jwt-token-" + str(int(time.time())),
            token_type="bearer",
            user=user
        )
    elif request.email == "<EMAIL>" and request.password == "user123":
        user = User(
            id=2,
            email="<EMAIL>",
            full_name="普通用户",
            is_active=True,
            is_admin=False,
            created_at=datetime.now().isoformat()
        )
        return LoginResponse(
            access_token="mock-jwt-token-" + str(int(time.time())),
            token_type="bearer",
            user=user
        )
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

@app.get("/api/v1/auth/me", response_model=User)
def get_current_user():
    return User(
        id=1,
        email="<EMAIL>",
        full_name="管理员",
        is_active=True,
        is_admin=True,
        created_at=datetime.now().isoformat()
    )

# USDT价格API
@app.get("/api/v1/usdt/current")
def get_current_price():
    return {
        "id": 1,
        "price": generate_mock_price(),
        "source": "mock",
        "timestamp": datetime.now().isoformat(),
        "volume_24h": random.uniform(1000000, 5000000),
        "market_cap": random.uniform(80000000000, 90000000000)
    }

@app.get("/api/v1/usdt/history")
def get_price_history(hours: int = 24):
    return {"data": generate_mock_history(hours)}

@app.get("/api/v1/usdt/stats")
def get_price_stats():
    current = generate_mock_price()
    price_24h_ago = generate_mock_price()
    change = current - price_24h_ago
    change_percent = (change / price_24h_ago) * 100 if price_24h_ago != 0 else 0

    return {
        "current_price": current,
        "price_24h_ago": price_24h_ago,
        "change_24h": round(change, 4),
        "change_24h_percent": round(change_percent, 2),
        "high_24h": round(current + random.uniform(0, 0.02), 4),
        "low_24h": round(current - random.uniform(0, 0.02), 4),
        "volume_24h": random.uniform(1000000, 5000000),
        "market_cap": random.uniform(80000000000, 90000000000),
        "last_updated": datetime.now().isoformat()
    }

@app.get("/api/v1/usdt/chart")
def get_chart_data(interval: str = "1h", limit: int = 24):
    data = []
    now = datetime.now()

    for i in range(limit):
        if interval == "1h":
            timestamp = now - timedelta(hours=limit-i)
        elif interval == "1d":
            timestamp = now - timedelta(days=limit-i)
        else:
            timestamp = now - timedelta(hours=limit-i)

        price = generate_mock_price()
        data.append({
            "timestamp": timestamp.isoformat(),
            "price": price,
            "volume": random.uniform(100000, 500000),
            "high": price + random.uniform(0, 0.01),
            "low": price - random.uniform(0, 0.01),
            "open": price + random.uniform(-0.005, 0.005),
            "close": price
        })

    return {"data": data}

# 告警API
@app.get("/api/v1/alerts")
def get_alerts():
    return {
        "data": [
            {
                "id": 1,
                "type": "price_above",
                "threshold": 1.01,
                "is_active": True,
                "name": "价格过高告警",
                "description": "当USDT价格超过1.01时触发",
                "created_at": datetime.now().isoformat(),
                "triggered_count": random.randint(0, 10)
            },
            {
                "id": 2,
                "type": "price_below",
                "threshold": 0.99,
                "is_active": False,
                "name": "价格过低告警",
                "description": "当USDT价格低于0.99时触发",
                "created_at": (datetime.now() - timedelta(days=1)).isoformat(),
                "triggered_count": random.randint(0, 5)
            }
        ]
    }

@app.post("/api/v1/alerts")
def create_alert(alert_data: dict):
    return {
        "id": random.randint(100, 999),
        "message": "告警创建成功",
        "created_at": datetime.now().isoformat(),
        **alert_data
    }

@app.put("/api/v1/alerts/{alert_id}")
def update_alert(alert_id: int, alert_data: dict):
    return {
        "id": alert_id,
        "message": "告警更新成功",
        "updated_at": datetime.now().isoformat(),
        **alert_data
    }

@app.delete("/api/v1/alerts/{alert_id}")
def delete_alert(alert_id: int):
    return {"message": f"告警 {alert_id} 删除成功"}

# 导出API
@app.post("/api/v1/usdt/export")
def create_export(export_data: dict):
    return {
        "export_id": f"export_{int(time.time())}",
        "status": "processing",
        "progress": 0,
        "created_at": datetime.now().isoformat(),
        "estimated_completion": (datetime.now() + timedelta(minutes=2)).isoformat()
    }

@app.get("/api/v1/usdt/export/{export_id}")
def get_export_status(export_id: str):
    # 模拟导出进度
    progress = random.randint(50, 100)
    status = "completed" if progress == 100 else "processing"

    return {
        "export_id": export_id,
        "status": status,
        "progress": progress,
        "download_url": f"/api/v1/usdt/export/{export_id}/download" if status == "completed" else None,
        "file_size": random.randint(1024, 10240) if status == "completed" else None
    }

if __name__ == "__main__":
    import uvicorn
    print("Starting USDT Monitor API server on port 8001...")
    print("API Documentation: http://localhost:8001/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001)
