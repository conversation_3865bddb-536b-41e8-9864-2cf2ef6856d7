from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Test Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "Hello World"}

@app.get("/api/v1/auth/login")
def login():
    return {
        "access_token": "test-token",
        "token_type": "bearer",
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "full_name": "Test User",
            "is_active": True,
            "is_admin": True
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("Starting server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
