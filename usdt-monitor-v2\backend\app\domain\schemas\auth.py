"""
认证相关的Pydantic schemas
"""
import uuid
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, validator


class LoginRequest(BaseModel):
    """登录请求schema"""
    username: str = Field(..., min_length=1, max_length=50)
    password: str = Field(..., min_length=1, max_length=128)
    remember_me: bool = False
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        return v.strip().lower()


class LoginResponse(BaseModel):
    """登录响应schema"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: "UserPublic"  # 前向引用，需要在文件末尾更新


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求schema"""
    refresh_token: str = Field(..., min_length=1)


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应schema"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RegisterRequest(BaseModel):
    """注册请求schema"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)
    full_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v.lower()
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'password' in values and v != values['password']:
            raise ValueError('密码确认不匹配')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        from app.core.security import validate_password_strength
        
        result = validate_password_strength(v)
        if not result['is_valid']:
            raise ValueError('; '.join(result['errors']))
        return v


class RegisterResponse(BaseModel):
    """注册响应schema"""
    message: str
    user_id: uuid.UUID
    verification_required: bool = True


class PasswordResetRequest(BaseModel):
    """密码重置请求schema"""
    email: EmailStr


class PasswordResetResponse(BaseModel):
    """密码重置响应schema"""
    message: str


class PasswordResetConfirm(BaseModel):
    """密码重置确认schema"""
    token: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码确认不匹配')
        return v
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """验证新密码强度"""
        from app.core.security import validate_password_strength
        
        result = validate_password_strength(v)
        if not result['is_valid']:
            raise ValueError('; '.join(result['errors']))
        return v


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求schema"""
    token: str = Field(..., min_length=1)


class EmailVerificationResponse(BaseModel):
    """邮箱验证响应schema"""
    message: str
    verified: bool


class ResendVerificationRequest(BaseModel):
    """重发验证邮件请求schema"""
    email: EmailStr


class ResendVerificationResponse(BaseModel):
    """重发验证邮件响应schema"""
    message: str


class TokenData(BaseModel):
    """令牌数据schema"""
    user_id: Optional[uuid.UUID] = None
    username: Optional[str] = None
    scopes: list[str] = []


class PasswordStrengthCheck(BaseModel):
    """密码强度检查schema"""
    password: str = Field(..., min_length=1, max_length=128)


class PasswordStrengthResponse(BaseModel):
    """密码强度检查响应schema"""
    is_valid: bool
    strength: str  # weak, medium, strong, very_strong
    errors: list[str]
    suggestions: list[str]


class LogoutRequest(BaseModel):
    """登出请求schema"""
    refresh_token: Optional[str] = None


class LogoutResponse(BaseModel):
    """登出响应schema"""
    message: str


class TwoFactorSetupRequest(BaseModel):
    """双因素认证设置请求schema"""
    password: str = Field(..., min_length=1)


class TwoFactorSetupResponse(BaseModel):
    """双因素认证设置响应schema"""
    secret: str
    qr_code_url: str
    backup_codes: list[str]


class TwoFactorVerifyRequest(BaseModel):
    """双因素认证验证请求schema"""
    code: str = Field(..., min_length=6, max_length=6)
    
    @validator('code')
    def validate_code(cls, v):
        """验证验证码格式"""
        if not v.isdigit():
            raise ValueError('验证码必须是6位数字')
        return v


class TwoFactorVerifyResponse(BaseModel):
    """双因素认证验证响应schema"""
    verified: bool
    message: str


class SessionInfo(BaseModel):
    """会话信息schema"""
    user_id: uuid.UUID
    username: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: str
    last_activity: str
    expires_at: str


class ActiveSessionsResponse(BaseModel):
    """活跃会话响应schema"""
    sessions: list[SessionInfo]
    current_session_id: str


# 添加新的schemas以匹配API端点
class Token(BaseModel):
    """令牌响应schema"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class UserLogin(BaseModel):
    """用户登录schema"""
    email: EmailStr
    password: str = Field(..., min_length=1)


class UserRegister(BaseModel):
    """用户注册schema"""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = Field(None, max_length=100)
    password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'password' in values and v != values['password']:
            raise ValueError('密码确认不匹配')
        return v


class PasswordChange(BaseModel):
    """密码修改schema"""
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码确认不匹配')
        return v


class PasswordReset(BaseModel):
    """密码重置schema"""
    token: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证密码确认"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码确认不匹配')
        return v


class RefreshToken(BaseModel):
    """刷新令牌schema"""
    refresh_token: str = Field(..., min_length=1)


# 更新前向引用
from app.domain.schemas.user import UserPublic
LoginResponse.model_rebuild()
