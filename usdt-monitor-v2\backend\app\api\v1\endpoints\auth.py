"""
认证相关API端点
"""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from app.api.deps import get_current_user, get_current_active_user
from app.core.config import settings
from app.core.security import create_access_token, verify_password
from app.domain.schemas.auth import (
    Token, TokenData, UserLogin, UserRegister, 
    PasswordReset, PasswordChange, RefreshToken
)
from app.domain.schemas.common import ApiResponse
from app.domain.schemas.user import User, UserCreate
from app.application.services.auth_service import AuthService
from app.application.services.user_service import UserService

router = APIRouter()


@router.post("/login", response_model=ApiResponse[Token])
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends()
) -> Any:
    """
    用户登录
    """
    try:
        # 验证用户凭据
        user = await auth_service.authenticate_user(
            email=form_data.username,
            password=form_data.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账户已被禁用"
            )
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        
        # 创建刷新令牌
        refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        refresh_token = create_access_token(
            data={"sub": user.email, "type": "refresh"}, 
            expires_delta=refresh_token_expires
        )
        
        # 记录登录活动
        await auth_service.log_login_activity(user.id, success=True)
        
        return ApiResponse(
            data=Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            ),
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # 记录失败的登录尝试
        await auth_service.log_login_activity(None, success=False, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生错误"
        )


@router.post("/register", response_model=ApiResponse[User])
async def register(
    user_data: UserRegister,
    user_service: UserService = Depends()
) -> Any:
    """
    用户注册
    """
    try:
        # 检查邮箱是否已存在
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被注册"
            )
        
        # 创建用户
        user_create = UserCreate(
            email=user_data.email,
            username=user_data.username,
            full_name=user_data.full_name,
            password=user_data.password
        )
        
        user = await user_service.create_user(user_create)
        
        return ApiResponse(
            data=user,
            message="注册成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册过程中发生错误"
        )


@router.post("/refresh", response_model=ApiResponse[Token])
async def refresh_token(
    refresh_data: RefreshToken,
    auth_service: AuthService = Depends()
) -> Any:
    """
    刷新访问令牌
    """
    try:
        # 验证刷新令牌
        token_data = await auth_service.verify_refresh_token(refresh_data.refresh_token)
        
        # 获取用户信息
        user = await auth_service.get_user_by_email(token_data.email)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        
        return ApiResponse(
            data=Token(
                access_token=access_token,
                refresh_token=refresh_data.refresh_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            ),
            message="令牌刷新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败"
        )


@router.get("/me", response_model=ApiResponse[User])
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取当前用户信息
    """
    return ApiResponse(
        data=current_user,
        message="获取用户信息成功"
    )


@router.post("/logout", response_model=ApiResponse[dict])
async def logout(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends()
) -> Any:
    """
    用户登出
    """
    try:
        # 记录登出活动
        await auth_service.log_logout_activity(current_user.id)
        
        return ApiResponse(
            data={"message": "已成功登出"},
            message="登出成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出过程中发生错误"
        )


@router.post("/change-password", response_model=ApiResponse[dict])
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends()
) -> Any:
    """
    修改密码
    """
    try:
        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )
        
        # 更新密码
        await auth_service.change_password(current_user.id, password_data.new_password)
        
        return ApiResponse(
            data={"message": "密码修改成功"},
            message="密码修改成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.post("/forgot-password", response_model=ApiResponse[dict])
async def forgot_password(
    email_data: dict,
    auth_service: AuthService = Depends()
) -> Any:
    """
    忘记密码
    """
    try:
        email = email_data.get("email")
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱地址不能为空"
            )
        
        # 发送重置密码邮件
        await auth_service.send_password_reset_email(email)
        
        return ApiResponse(
            data={"message": "重置密码邮件已发送"},
            message="如果该邮箱存在，重置密码邮件已发送"
        )
        
    except Exception as e:
        # 不暴露具体错误信息，防止邮箱枚举攻击
        return ApiResponse(
            data={"message": "重置密码邮件已发送"},
            message="如果该邮箱存在，重置密码邮件已发送"
        )


@router.post("/reset-password", response_model=ApiResponse[dict])
async def reset_password(
    reset_data: PasswordReset,
    auth_service: AuthService = Depends()
) -> Any:
    """
    重置密码
    """
    try:
        # 验证重置令牌并重置密码
        await auth_service.reset_password(reset_data.token, reset_data.new_password)
        
        return ApiResponse(
            data={"message": "密码重置成功"},
            message="密码重置成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码重置失败"
        )
