import React from 'react'
import { Spin, SpinProps } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import classNames from 'classnames'

interface LoadingProps extends SpinProps {
  fullscreen?: boolean
  overlay?: boolean
  text?: string
  className?: string
}

export const Loading: React.FC<LoadingProps> = ({
  fullscreen = false,
  overlay = false,
  text = '加载中...',
  className,
  ...props
}) => {
  const loadingIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

  const spinElement = (
    <Spin
      indicator={loadingIcon}
      tip={text}
      size="large"
      {...props}
    />
  )

  if (fullscreen) {
    return (
      <div className={classNames(
        'fixed inset-0 z-50 flex items-center justify-center',
        overlay ? 'bg-black bg-opacity-50' : 'bg-white',
        className
      )}>
        {spinElement}
      </div>
    )
  }

  return (
    <div className={classNames(
      'flex items-center justify-center p-8',
      className
    )}>
      {spinElement}
    </div>
  )
}

// 页面级加载组件
export const PageLoading: React.FC<{ text?: string }> = ({ text = '页面加载中...' }) => (
  <Loading fullscreen text={text} />
)

// 内容加载组件
export const ContentLoading: React.FC<{ text?: string }> = ({ text = '内容加载中...' }) => (
  <Loading text={text} className="min-h-[200px]" />
)

// 按钮加载组件
export const ButtonLoading: React.FC = () => (
  <LoadingOutlined className="mr-2" />
)
