"""
认证服务
"""
from datetime import datetime, timedelta
from typing import Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from jose import JWTError, jwt

from app.core.config import settings
from app.core.security import verify_password, get_password_hash, create_access_token
from app.core.exceptions import AppException
from app.domain.schemas.user import User
from app.domain.schemas.auth import TokenData
from app.infrastructure.repositories.user_repository import UserRepository


class AuthService:
    """认证服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.user_repository = UserRepository(db)
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        try:
            # 获取用户
            user_model = await self.user_repository.get_by_email(email)
            if not user_model:
                return None
            
            # 检查用户是否被锁定
            if user_model.is_locked:
                raise AppException(
                    error_code="USER_LOCKED",
                    message="用户账户已被锁定",
                    details={"locked_until": user_model.locked_until}
                )
            
            # 验证密码
            if not verify_password(password, user_model.hashed_password):
                # 增加失败登录次数
                failed_attempts = await self.user_repository.increment_failed_login_attempts(user_model.id)
                
                # 如果失败次数过多，锁定账户
                if failed_attempts >= 5:
                    lock_until = datetime.utcnow() + timedelta(hours=1)
                    await self.user_repository.lock_user(user_model.id, lock_until)
                    
                    raise AppException(
                        error_code="USER_LOCKED_DUE_TO_FAILED_ATTEMPTS",
                        message="由于多次登录失败，账户已被锁定1小时"
                    )
                
                return None
            
            # 更新最后登录时间
            await self.user_repository.update_last_login(user_model.id)
            
            # 转换为schema
            from app.domain.schemas.user import User
            return User.from_orm(user_model)
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="AUTHENTICATION_ERROR",
                message="认证过程中发生错误",
                details={"error": str(e)}
            )
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            user_model = await self.user_repository.get_by_email(email)
            if user_model:
                from app.domain.schemas.user import User
                return User.from_orm(user_model)
            return None
            
        except Exception as e:
            raise AppException(
                error_code="USER_FETCH_ERROR",
                message="获取用户信息失败",
                details={"error": str(e)}
            )
    
    async def verify_refresh_token(self, refresh_token: str) -> TokenData:
        """验证刷新令牌"""
        try:
            payload = jwt.decode(
                refresh_token, 
                settings.SECRET_KEY, 
                algorithms=[settings.ALGORITHM]
            )
            
            email: str = payload.get("sub")
            token_type: str = payload.get("type")
            
            if email is None or token_type != "refresh":
                raise AppException(
                    error_code="INVALID_REFRESH_TOKEN",
                    message="无效的刷新令牌"
                )
            
            return TokenData(email=email)
            
        except JWTError:
            raise AppException(
                error_code="INVALID_REFRESH_TOKEN",
                message="无效的刷新令牌"
            )
    
    async def change_password(self, user_id: UUID, new_password: str) -> bool:
        """修改用户密码"""
        try:
            # 验证密码强度
            from app.core.security import validate_password_strength
            validation_result = validate_password_strength(new_password)
            
            if not validation_result['is_valid']:
                raise AppException(
                    error_code="WEAK_PASSWORD",
                    message="密码强度不足",
                    details={"errors": validation_result['errors']}
                )
            
            # 更新密码
            success = await self.user_repository.change_password(user_id, new_password)
            
            if success:
                # 记录密码修改活动
                await self.user_repository.create_user_activity(
                    user_id=user_id,
                    action="password_changed",
                    details="用户修改了密码"
                )
            
            return success
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="PASSWORD_CHANGE_ERROR",
                message="修改密码失败",
                details={"error": str(e)}
            )
    
    async def send_password_reset_email(self, email: str) -> bool:
        """发送密码重置邮件"""
        try:
            # 检查用户是否存在
            user_model = await self.user_repository.get_by_email(email)
            if not user_model:
                # 为了安全，不暴露用户是否存在
                return True
            
            # 生成重置令牌
            reset_token = create_access_token(
                data={"sub": email, "type": "password_reset"},
                expires_delta=timedelta(hours=1)
            )
            
            # TODO: 实现邮件发送逻辑
            # await self.email_service.send_password_reset_email(email, reset_token)
            
            # 记录活动
            await self.user_repository.create_user_activity(
                user_id=user_model.id,
                action="password_reset_requested",
                details="用户请求重置密码"
            )
            
            return True
            
        except Exception as e:
            raise AppException(
                error_code="PASSWORD_RESET_EMAIL_ERROR",
                message="发送重置邮件失败",
                details={"error": str(e)}
            )
    
    async def reset_password(self, reset_token: str, new_password: str) -> bool:
        """重置密码"""
        try:
            # 验证重置令牌
            try:
                payload = jwt.decode(
                    reset_token, 
                    settings.SECRET_KEY, 
                    algorithms=[settings.ALGORITHM]
                )
                
                email: str = payload.get("sub")
                token_type: str = payload.get("type")
                
                if email is None or token_type != "password_reset":
                    raise AppException(
                        error_code="INVALID_RESET_TOKEN",
                        message="无效的重置令牌"
                    )
                    
            except JWTError:
                raise AppException(
                    error_code="INVALID_RESET_TOKEN",
                    message="无效的重置令牌"
                )
            
            # 获取用户
            user_model = await self.user_repository.get_by_email(email)
            if not user_model:
                raise AppException(
                    error_code="USER_NOT_FOUND",
                    message="用户不存在"
                )
            
            # 验证新密码强度
            from app.core.security import validate_password_strength
            validation_result = validate_password_strength(new_password)
            
            if not validation_result['is_valid']:
                raise AppException(
                    error_code="WEAK_PASSWORD",
                    message="密码强度不足",
                    details={"errors": validation_result['errors']}
                )
            
            # 重置密码
            success = await self.user_repository.change_password(user_model.id, new_password)
            
            if success:
                # 解锁用户（如果被锁定）
                await self.user_repository.unlock_user(user_model.id)
                
                # 记录活动
                await self.user_repository.create_user_activity(
                    user_id=user_model.id,
                    action="password_reset_completed",
                    details="用户通过重置令牌修改了密码"
                )
            
            return success
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="PASSWORD_RESET_ERROR",
                message="重置密码失败",
                details={"error": str(e)}
            )
    
    async def log_login_activity(
        self, 
        user_id: Optional[UUID], 
        success: bool, 
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error: Optional[str] = None
    ) -> None:
        """记录登录活动"""
        try:
            if user_id:
                action = "login_success" if success else "login_failed"
                details = f"登录{'成功' if success else '失败'}"
                
                if error and not success:
                    details += f": {error}"
                
                await self.user_repository.create_user_activity(
                    user_id=user_id,
                    action=action,
                    details=details,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
        except Exception as e:
            # 记录活动失败不应该影响主要流程
            import structlog
            logger = structlog.get_logger()
            logger.error("Failed to log login activity", error=str(e))
    
    async def log_logout_activity(
        self, 
        user_id: UUID,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """记录登出活动"""
        try:
            await self.user_repository.create_user_activity(
                user_id=user_id,
                action="logout",
                details="用户登出",
                ip_address=ip_address,
                user_agent=user_agent
            )
            
        except Exception as e:
            # 记录活动失败不应该影响主要流程
            import structlog
            logger = structlog.get_logger()
            logger.error("Failed to log logout activity", error=str(e))
    
    async def verify_user_email(self, verification_token: str) -> bool:
        """验证用户邮箱"""
        try:
            # 验证令牌
            try:
                payload = jwt.decode(
                    verification_token, 
                    settings.SECRET_KEY, 
                    algorithms=[settings.ALGORITHM]
                )
                
                email: str = payload.get("sub")
                token_type: str = payload.get("type")
                
                if email is None or token_type != "email_verification":
                    raise AppException(
                        error_code="INVALID_VERIFICATION_TOKEN",
                        message="无效的验证令牌"
                    )
                    
            except JWTError:
                raise AppException(
                    error_code="INVALID_VERIFICATION_TOKEN",
                    message="无效的验证令牌"
                )
            
            # 获取用户
            user_model = await self.user_repository.get_by_email(email)
            if not user_model:
                raise AppException(
                    error_code="USER_NOT_FOUND",
                    message="用户不存在"
                )
            
            # 验证邮箱
            success = await self.user_repository.verify_user(user_model.id)
            
            if success:
                # 记录活动
                await self.user_repository.create_user_activity(
                    user_id=user_model.id,
                    action="email_verified",
                    details="用户验证了邮箱"
                )
            
            return success
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="EMAIL_VERIFICATION_ERROR",
                message="邮箱验证失败",
                details={"error": str(e)}
            )
    
    async def send_verification_email(self, email: str) -> bool:
        """发送验证邮件"""
        try:
            # 检查用户是否存在
            user_model = await self.user_repository.get_by_email(email)
            if not user_model:
                raise AppException(
                    error_code="USER_NOT_FOUND",
                    message="用户不存在"
                )
            
            # 检查是否已验证
            if user_model.is_verified:
                raise AppException(
                    error_code="EMAIL_ALREADY_VERIFIED",
                    message="邮箱已经验证过了"
                )
            
            # 生成验证令牌
            verification_token = create_access_token(
                data={"sub": email, "type": "email_verification"},
                expires_delta=timedelta(hours=24)
            )
            
            # TODO: 实现邮件发送逻辑
            # await self.email_service.send_verification_email(email, verification_token)
            
            # 记录活动
            await self.user_repository.create_user_activity(
                user_id=user_model.id,
                action="verification_email_sent",
                details="发送了邮箱验证邮件"
            )
            
            return True
            
        except AppException:
            raise
        except Exception as e:
            raise AppException(
                error_code="VERIFICATION_EMAIL_ERROR",
                message="发送验证邮件失败",
                details={"error": str(e)}
            )
