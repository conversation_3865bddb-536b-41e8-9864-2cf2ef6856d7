# USDT Monitor Platform V2 🚀

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/yourusername/usdt-monitor-v2)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org)

> 🔥 **企业级USDT价格监控平台** - 实时监控、智能告警、数据分析一体化解决方案

## 🚀 技术栈

### 后端
- **FastAPI** - 现代、高性能的Python Web框架
- **SQLAlchemy 2.0** - 现代Python ORM
- **PostgreSQL** - 生产级关系数据库
- **Redis** - 缓存和会话存储
- **Pydantic v2** - 数据验证和序列化
- **Alembic** - 数据库迁移工具
- **pytest** - 测试框架

### 前端
- **React 18** - 现代React框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **TanStack Query** - 强大的数据获取库
- **Zustand** - 轻量级状态管理
- **Ant Design 5** - 企业级UI组件库
- **React Hook Form** - 高性能表单库
- **Vitest** - 快速测试框架

## 🏗️ 架构设计

### 设计原则
- **领域驱动设计 (DDD)** - 清晰的业务领域建模
- **清洁架构** - 分层架构，依赖倒置
- **CQRS模式** - 命令查询分离
- **事件驱动** - 松耦合的组件通信
- **安全优先** - 零信任安全架构

### 项目结构
```
usdt-monitor-v2/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── core/           # 核心配置和基础设施
│   │   ├── domain/         # 领域模型和业务逻辑
│   │   ├── infrastructure/ # 基础设施层
│   │   ├── application/    # 应用服务层
│   │   ├── api/           # API接口层
│   │   └── tests/         # 测试
│   ├── migrations/        # 数据库迁移
│   └── scripts/          # 脚本工具
├── frontend/              # 前端应用
│   ├── src/
│   │   ├── components/    # 可复用组件
│   │   ├── pages/        # 页面组件
│   │   ├── hooks/        # 自定义hooks
│   │   ├── services/     # API服务
│   │   ├── stores/       # 状态管理
│   │   ├── types/        # 类型定义
│   │   └── utils/        # 工具函数
│   └── tests/           # 测试
├── shared/              # 共享类型和工具
├── docs/               # 项目文档
├── scripts/            # 构建和部署脚本
└── docker-compose.yml  # 开发环境配置
```

## 🔒 安全特性

- **JWT认证** - 安全的无状态认证
- **RBAC权限控制** - 基于角色的访问控制
- **输入验证** - 严格的数据验证
- **SQL注入防护** - 参数化查询
- **XSS防护** - 内容安全策略
- **CSRF防护** - 跨站请求伪造防护
- **速率限制** - API请求频率控制
- **安全头** - 完整的HTTP安全头

## ⚡ 性能优化

- **数据库优化** - 索引优化、查询优化
- **缓存策略** - Redis多层缓存
- **异步处理** - 非阻塞I/O操作
- **连接池** - 数据库连接池管理
- **代码分割** - 前端按需加载
- **CDN加速** - 静态资源加速
- **压缩优化** - Gzip/Brotli压缩

## 🧪 测试策略

- **单元测试** - 业务逻辑测试
- **集成测试** - API接口测试
- **E2E测试** - 端到端功能测试
- **性能测试** - 负载和压力测试
- **安全测试** - 漏洞扫描测试

## 📊 监控和日志

- **应用监控** - 性能指标监控
- **错误追踪** - 异常和错误追踪
- **日志聚合** - 结构化日志收集
- **告警系统** - 实时告警通知
- **健康检查** - 服务健康状态检查

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose

### 开发环境启动
```bash
# 克隆项目
git clone <repository-url>
cd usdt-monitor-v2

# 启动开发环境
docker-compose up -d

# 安装后端依赖
cd backend
pip install -r requirements.txt

# 运行数据库迁移
alembic upgrade head

# 启动后端服务
uvicorn app.main:app --reload

# 安装前端依赖
cd ../frontend
npm install

# 启动前端开发服务器
npm run dev
```

### 生产环境部署
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 文档

- [API文档](docs/api.md) - REST API接口文档
- [架构设计](docs/architecture.md) - 系统架构设计
- [部署指南](docs/deployment.md) - 部署和运维指南
- [开发指南](docs/development.md) - 开发规范和指南
- [安全指南](docs/security.md) - 安全最佳实践

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和开源社区。

---

**构建高质量、安全、可扩展的USDT监控平台** 🚀
