import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Result, Button } from 'antd'
import { useAuthStore } from '../stores/authStore'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
  requiredPermissions?: string[]
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  requiredPermissions = [],
  fallback,
}) => {
  const location = useLocation()
  const { isAuthenticated, user, hasPermission, isAdmin } = useAuthStore()

  // 未登录，重定向到登录页
  if (!isAuthenticated || !user) {
    return (
      <Navigate
        to="/auth/login"
        state={{ from: location }}
        replace
      />
    )
  }

  // 需要管理员权限但用户不是管理员
  if (requireAdmin && !isAdmin()) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <Result
          status="403"
          title="权限不足"
          subTitle="您没有访问此页面的权限，请联系管理员。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      </div>
    )
  }

  // 检查特定权限
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission)
    )

    if (!hasAllPermissions) {
      return fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <Result
            status="403"
            title="权限不足"
            subTitle="您没有执行此操作的权限。"
            extra={
              <Button type="primary" onClick={() => window.history.back()}>
                返回上一页
              </Button>
            }
          />
        </div>
      )
    }
  }

  // 权限检查通过，渲染子组件
  return <>{children}</>
}
