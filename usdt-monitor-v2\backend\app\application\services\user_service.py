"""
用户服务
"""
from typing import List, Optional, Tuple
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from app.core.security import get_password_hash, verify_password
from app.domain.models.user import User as UserModel
from app.domain.schemas.user import User, UserCreate, UserUpdate, UserProfile
from app.core.exceptions import AppException


class UserService:
    """用户服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """根据ID获取用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if user_model:
                return User.from_orm(user_model)
            return None
            
        except Exception as e:
            raise AppException(
                error_code="USER_FETCH_ERROR",
                message="获取用户信息失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.email == email)
            )
            user_model = result.scalar_one_or_none()
            
            if user_model:
                return User.from_orm(user_model)
            return None
            
        except Exception as e:
            raise AppException(
                error_code="USER_FETCH_ERROR",
                message="获取用户信息失败",
                details={"email": email, "error": str(e)}
            )
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.username == username)
            )
            user_model = result.scalar_one_or_none()
            
            if user_model:
                return User.from_orm(user_model)
            return None
            
        except Exception as e:
            raise AppException(
                error_code="USER_FETCH_ERROR",
                message="获取用户信息失败",
                details={"username": username, "error": str(e)}
            )
    
    async def create_user(self, user_create: UserCreate) -> User:
        """创建用户"""
        try:
            # 检查邮箱是否已存在
            existing_user = await self.get_user_by_email(user_create.email)
            if existing_user:
                raise AppException(
                    error_code="EMAIL_ALREADY_EXISTS",
                    message="该邮箱已被注册"
                )
            
            # 检查用户名是否已存在
            if user_create.username:
                existing_username = await self.get_user_by_username(user_create.username)
                if existing_username:
                    raise AppException(
                        error_code="USERNAME_ALREADY_EXISTS",
                        message="该用户名已被使用"
                    )
            
            # 创建用户模型
            user_model = UserModel(
                email=user_create.email,
                username=user_create.username,
                full_name=user_create.full_name,
                hashed_password=get_password_hash(user_create.password),
                is_active=True,
                is_superuser=getattr(user_create, 'is_superuser', False)
            )
            
            self.db.add(user_model)
            await self.db.commit()
            await self.db.refresh(user_model)
            
            return User.from_orm(user_model)
            
        except AppException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_CREATE_ERROR",
                message="创建用户失败",
                details={"error": str(e)}
            )
    
    async def update_user(self, user_id: UUID, user_update: UserUpdate) -> User:
        """更新用户信息"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                raise AppException(
                    error_code="USER_NOT_FOUND",
                    message="用户不存在"
                )
            
            # 更新字段
            update_data = user_update.dict(exclude_unset=True)
            for field, value in update_data.items():
                if field == "password" and value:
                    setattr(user_model, "hashed_password", get_password_hash(value))
                else:
                    setattr(user_model, field, value)
            
            await self.db.commit()
            await self.db.refresh(user_model)
            
            return User.from_orm(user_model)
            
        except AppException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_UPDATE_ERROR",
                message="更新用户信息失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def update_user_profile(self, user_id: UUID, user_update: UserProfile) -> User:
        """更新用户个人资料"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                raise AppException(
                    error_code="USER_NOT_FOUND",
                    message="用户不存在"
                )
            
            # 更新允许的字段
            update_data = user_update.dict(exclude_unset=True)
            allowed_fields = ["full_name", "phone", "avatar_url", "bio", "timezone"]
            
            for field, value in update_data.items():
                if field in allowed_fields:
                    setattr(user_model, field, value)
            
            await self.db.commit()
            await self.db.refresh(user_model)
            
            return User.from_orm(user_model)
            
        except AppException:
            raise
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_PROFILE_UPDATE_ERROR",
                message="更新用户资料失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def delete_user(self, user_id: UUID) -> bool:
        """删除用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                return False
            
            await self.db.delete(user_model)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_DELETE_ERROR",
                message="删除用户失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def activate_user(self, user_id: UUID) -> Optional[User]:
        """激活用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                return None
            
            user_model.is_active = True
            await self.db.commit()
            await self.db.refresh(user_model)
            
            return User.from_orm(user_model)
            
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_ACTIVATE_ERROR",
                message="激活用户失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def deactivate_user(self, user_id: UUID) -> Optional[User]:
        """停用用户"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                return None
            
            user_model.is_active = False
            await self.db.commit()
            await self.db.refresh(user_model)
            
            return User.from_orm(user_model)
            
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="USER_DEACTIVATE_ERROR",
                message="停用用户失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def get_users(
        self,
        offset: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        role: Optional[str] = None
    ) -> Tuple[List[User], int]:
        """获取用户列表"""
        try:
            # 构建查询条件
            conditions = []
            
            if search:
                search_condition = or_(
                    UserModel.email.ilike(f"%{search}%"),
                    UserModel.username.ilike(f"%{search}%"),
                    UserModel.full_name.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
            
            if is_active is not None:
                conditions.append(UserModel.is_active == is_active)
            
            if role:
                if role == "admin":
                    conditions.append(UserModel.is_superuser == True)
                elif role == "user":
                    conditions.append(UserModel.is_superuser == False)
            
            # 构建查询
            query = select(UserModel)
            if conditions:
                query = query.where(and_(*conditions))
            
            # 获取总数
            count_query = select(func.count(UserModel.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()
            
            # 获取分页数据
            query = query.offset(offset).limit(limit).order_by(UserModel.created_at.desc())
            result = await self.db.execute(query)
            user_models = result.scalars().all()
            
            users = [User.from_orm(user_model) for user_model in user_models]
            
            return users, total
            
        except Exception as e:
            raise AppException(
                error_code="USER_LIST_ERROR",
                message="获取用户列表失败",
                details={"error": str(e)}
            )
    
    async def change_password(self, user_id: UUID, new_password: str) -> bool:
        """修改用户密码"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                return False
            
            user_model.hashed_password = get_password_hash(new_password)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise AppException(
                error_code="PASSWORD_CHANGE_ERROR",
                message="修改密码失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
    
    async def verify_password(self, user_id: UUID, password: str) -> bool:
        """验证用户密码"""
        try:
            result = await self.db.execute(
                select(UserModel).where(UserModel.id == user_id)
            )
            user_model = result.scalar_one_or_none()
            
            if not user_model:
                return False
            
            return verify_password(password, user_model.hashed_password)
            
        except Exception as e:
            raise AppException(
                error_code="PASSWORD_VERIFY_ERROR",
                message="验证密码失败",
                details={"user_id": str(user_id), "error": str(e)}
            )
