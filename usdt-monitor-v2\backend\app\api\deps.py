"""
API依赖项
"""
from typing import Generator, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.core.security import AL<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.domain.schemas.auth import TokenData
from app.domain.schemas.user import User
from app.application.services.user_service import UserService

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    获取当前用户
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解码JWT令牌
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        
        token_data = TokenData(email=email)
    except JWTError:
        raise credentials_exception
    
    # 获取用户服务
    user_service = UserService(db)
    user = await user_service.get_user_by_email(email=token_data.email)
    
    if user is None:
        raise credentials_exception
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前活跃用户
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    获取当前管理员用户
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


async def get_current_user_id(
    current_user: User = Depends(get_current_active_user)
) -> UUID:
    """
    获取当前用户ID
    """
    return current_user.id


def get_optional_current_user(
    token: Optional[str] = Depends(oauth2_scheme)
) -> Optional[User]:
    """
    获取可选的当前用户（用于可选认证的端点）
    """
    if not token:
        return None
    
    try:
        # 这里可以复用get_current_user的逻辑
        # 但需要处理异常，返回None而不是抛出异常
        pass
    except:
        return None


class RateLimiter:
    """
    速率限制器
    """
    def __init__(self, calls: int, period: int):
        self.calls = calls
        self.period = period
    
    async def __call__(self, request):
        # 这里应该实现实际的速率限制逻辑
        # 可以使用Redis来存储请求计数
        pass


def rate_limit(calls: int = 60, period: int = 60):
    """
    创建速率限制依赖
    """
    return RateLimiter(calls, period)


class PermissionChecker:
    """
    权限检查器
    """
    def __init__(self, required_permissions: list[str]):
        self.required_permissions = required_permissions
    
    async def __call__(self, current_user: User = Depends(get_current_active_user)):
        # 检查用户是否具有所需权限
        user_permissions = getattr(current_user, 'permissions', [])
        
        for permission in self.required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        
        return current_user


def require_permissions(*permissions: str):
    """
    创建权限检查依赖
    """
    return PermissionChecker(list(permissions))


class MaintenanceChecker:
    """
    维护模式检查器
    """
    async def __call__(self, current_user: Optional[User] = Depends(get_optional_current_user)):
        # 检查系统是否处于维护模式
        # 管理员用户可以在维护模式下访问
        from app.core.cache import cache_manager
        
        maintenance_mode = await cache_manager.get("system:maintenance_mode")
        if maintenance_mode and (not current_user or not current_user.is_superuser):
            maintenance_message = await cache_manager.get("system:maintenance_message")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=maintenance_message or "系统维护中，请稍后再试"
            )


def check_maintenance():
    """
    创建维护模式检查依赖
    """
    return MaintenanceChecker()


class IPWhitelistChecker:
    """
    IP白名单检查器
    """
    def __init__(self, whitelist: list[str]):
        self.whitelist = whitelist
    
    async def __call__(self, request):
        client_ip = request.client.host
        if client_ip not in self.whitelist:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="IP地址不在白名单中"
            )


def require_ip_whitelist(whitelist: list[str]):
    """
    创建IP白名单检查依赖
    """
    return IPWhitelistChecker(whitelist)


async def validate_api_key(
    api_key: Optional[str] = None
) -> bool:
    """
    验证API密钥
    """
    if not api_key:
        return False
    
    # 这里应该实现实际的API密钥验证逻辑
    # 可以从数据库或配置中获取有效的API密钥
    valid_api_keys = getattr(settings, 'VALID_API_KEYS', [])
    return api_key in valid_api_keys


class CacheControl:
    """
    缓存控制
    """
    def __init__(self, max_age: int = 300, public: bool = False):
        self.max_age = max_age
        self.public = public
    
    async def __call__(self, response):
        cache_control = f"max-age={self.max_age}"
        if self.public:
            cache_control = f"public, {cache_control}"
        else:
            cache_control = f"private, {cache_control}"
        
        response.headers["Cache-Control"] = cache_control
        return response


def cache_control(max_age: int = 300, public: bool = False):
    """
    创建缓存控制依赖
    """
    return CacheControl(max_age, public)


class RequestLogger:
    """
    请求日志记录器
    """
    async def __call__(self, request, current_user: Optional[User] = None):
        # 记录请求信息
        import structlog
        logger = structlog.get_logger()
        
        logger.info(
            "API request",
            method=request.method,
            url=str(request.url),
            user_id=current_user.id if current_user else None,
            user_email=current_user.email if current_user else None,
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )


def log_request():
    """
    创建请求日志依赖
    """
    return RequestLogger()


# 常用的依赖组合
CommonDeps = [
    Depends(check_maintenance()),
    Depends(log_request()),
]

AuthDeps = [
    Depends(get_current_active_user),
    *CommonDeps
]

AdminDeps = [
    Depends(get_current_admin_user),
    *CommonDeps
]
