import React, { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, Alert, Space, Button, Typography, Spin } from 'antd'
import { 
  DollarOutlined, 
  TrendingUpOutlined, 
  TrendingDownOutlined,
  WarningOutlined,
  ReloadOutlined,
  LineChartOutlined,
  BellOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { usdtApi } from '../services/api/usdt'
import { PriceChart } from '../components/charts/PriceChart'
import { StabilityIndicator } from '../components/indicators/StabilityIndicator'
import { AlertSummary } from '../components/alerts/AlertSummary'
import type { USDTData, USDTStats } from '../types/usdt'

const { Title, Text } = Typography

export default function Dashboard() {
  const [refreshing, setRefreshing] = useState(false)

  // 获取当前USDT数据
  const { 
    data: currentData, 
    isLoading: currentLoading,
    error: currentError,
    refetch: refetchCurrent 
  } = useQuery({
    queryKey: ['usdt', 'current'],
    queryFn: () => usdtApi.getCurrentData(),
    refetchInterval: 30000, // 30秒自动刷新
  })

  // 获取24小时统计数据
  const { 
    data: stats24h, 
    isLoading: statsLoading,
    refetch: refetchStats 
  } = useQuery({
    queryKey: ['usdt', 'stats', '24h'],
    queryFn: () => usdtApi.getStats('24h'),
    refetchInterval: 60000, // 1分钟自动刷新
  })

  // 获取图表数据
  const { 
    data: chartData, 
    isLoading: chartLoading,
    refetch: refetchChart 
  } = useQuery({
    queryKey: ['usdt', 'chart', '24h', '1h'],
    queryFn: () => usdtApi.getChartData('24h', '1h'),
    refetchInterval: 300000, // 5分钟自动刷新
  })

  // 手动刷新所有数据
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await Promise.all([
        refetchCurrent(),
        refetchStats(),
        refetchChart()
      ])
    } finally {
      setRefreshing(false)
    }
  }

  const current = currentData?.data
  const stats = stats24h?.data
  const isLoading = currentLoading || statsLoading || chartLoading

  // 计算价格变化趋势
  const getPriceTrend = (change?: number) => {
    if (!change) return 'stable'
    return change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
  }

  // 获取趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpOutlined className="text-green-500" />
      case 'down':
        return <TrendingDownOutlined className="text-red-500" />
      default:
        return <LineChartOutlined className="text-gray-500" />
    }
  }

  // 获取价格变化颜色
  const getPriceChangeColor = (change?: number) => {
    if (!change) return 'text-gray-500'
    return change > 0 ? 'text-green-500' : change < 0 ? 'text-red-500' : 'text-gray-500'
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            USDT监控仪表板
          </Title>
          <Text type="secondary">
            实时监控USDT价格变化和市场动态
          </Text>
        </div>
        <Button
          icon={<ReloadOutlined />}
          loading={refreshing}
          onClick={handleRefresh}
        >
          刷新数据
        </Button>
      </div>

      {/* 错误提示 */}
      {currentError && (
        <Alert
          message="数据加载失败"
          description="无法获取最新的USDT数据，请检查网络连接或稍后重试。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="当前价格"
              value={current?.current_price_usd || 0}
              precision={6}
              prefix={<DollarOutlined />}
              suffix="USD"
              loading={currentLoading}
              valueStyle={{ 
                color: current?.is_anomaly ? '#ff4d4f' : '#1890ff',
                fontSize: '24px'
              }}
            />
            {current?.is_anomaly && (
              <div className="mt-2">
                <Alert
                  message="价格异常"
                  type="warning"
                  size="small"
                  showIcon
                />
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="24小时变化"
              value={stats?.price_change_percentage || 0}
              precision={4}
              suffix="%"
              prefix={getTrendIcon(getPriceTrend(stats?.price_change))}
              loading={statsLoading}
              valueStyle={{ 
                color: getPriceChangeColor(stats?.price_change).replace('text-', ''),
                fontSize: '20px'
              }}
            />
            <div className="mt-2">
              <Text type="secondary" className="text-sm">
                {stats?.price_change && (
                  <span className={getPriceChangeColor(stats.price_change)}>
                    {stats.price_change > 0 ? '+' : ''}
                    ${stats.price_change.toFixed(6)}
                  </span>
                )}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="24小时交易量"
              value={stats?.total_volume || 0}
              formatter={(value) => `$${(Number(value) / 1e9).toFixed(2)}B`}
              prefix={<LineChartOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: '20px' }}
            />
            <div className="mt-2">
              <Text type="secondary" className="text-sm">
                平均: ${stats?.avg_volume ? (stats.avg_volume / 1e9).toFixed(2) : '0'}B
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="稳定性评分"
              value={stats?.stability_score || 0}
              precision={2}
              suffix="/1.0"
              prefix={<WarningOutlined />}
              loading={statsLoading}
              valueStyle={{ 
                color: (stats?.stability_score || 0) > 0.8 ? '#52c41a' : 
                       (stats?.stability_score || 0) > 0.6 ? '#faad14' : '#ff4d4f',
                fontSize: '20px'
              }}
            />
            <div className="mt-2">
              <StabilityIndicator score={stats?.stability_score || 0} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 详细统计信息 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card title="价格统计" size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex justify-between">
                <Text type="secondary">最高价:</Text>
                <Text strong>${stats?.max_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">最低价:</Text>
                <Text strong>${stats?.min_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">平均价:</Text>
                <Text strong>${stats?.avg_price?.toFixed(6) || '0'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">波动率:</Text>
                <Text strong>{((stats?.volatility || 0) * 100).toFixed(4)}%</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card title="市场数据" size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex justify-between">
                <Text type="secondary">市值:</Text>
                <Text strong>
                  ${current?.market_cap_usd ? (current.market_cap_usd / 1e9).toFixed(2) + 'B' : 'N/A'}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">流通量:</Text>
                <Text strong>
                  {current?.circulating_supply ? (current.circulating_supply / 1e9).toFixed(2) + 'B' : 'N/A'}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">数据源:</Text>
                <Text strong>{current?.data_source || 'N/A'}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">更新时间:</Text>
                <Text strong>
                  {current?.last_updated ? 
                    new Date(current.last_updated).toLocaleTimeString() : 'N/A'}
                </Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card title="告警摘要" size="small">
            <AlertSummary />
          </Card>
        </Col>
      </Row>

      {/* 价格图表 */}
      <Card title="24小时价格走势" loading={chartLoading}>
        {chartData?.data ? (
          <PriceChart 
            data={chartData.data} 
            height={400}
            showVolume={true}
          />
        ) : (
          <div className="flex items-center justify-center h-96">
            <Spin size="large" tip="加载图表数据..." />
          </div>
        )}
      </Card>

      {/* 快速操作 */}
      <Card title="快速操作">
        <Space wrap>
          <Button type="primary" icon={<LineChartOutlined />}>
            查看详细图表
          </Button>
          <Button icon={<BellOutlined />}>
            设置价格告警
          </Button>
          <Button icon={<TrendingUpOutlined />}>
            查看历史数据
          </Button>
          <Button icon={<DollarOutlined />}>
            导出数据
          </Button>
        </Space>
      </Card>
    </div>
  )
}
