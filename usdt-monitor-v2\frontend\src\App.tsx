import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout, Spin } from 'antd'
import { useAuthStore } from './stores/authStore'
import { useThemeStore } from './stores/themeStore'
import { ProtectedRoute } from './components/ProtectedRoute'
import { MainLayout } from './layouts/MainLayout'
import { AuthLayout } from './layouts/AuthLayout'

// 页面组件懒加载
const Dashboard = React.lazy(() => import('./pages/Dashboard'))
const Login = React.lazy(() => import('./pages/auth/Login'))
const Register = React.lazy(() => import('./pages/auth/Register'))
const ForgotPassword = React.lazy(() => import('./pages/auth/ForgotPassword'))
const ResetPassword = React.lazy(() => import('./pages/auth/ResetPassword'))
const USDTMonitor = React.lazy(() => import('./pages/usdt/Monitor'))
const USDTHistory = React.lazy(() => import('./pages/usdt/History'))
const USDTAlerts = React.lazy(() => import('./pages/usdt/Alerts'))
const USDTExport = React.lazy(() => import('./pages/usdt/Export'))
const UserProfile = React.lazy(() => import('./pages/user/Profile'))
const UserSettings = React.lazy(() => import('./pages/user/Settings'))
const AdminUsers = React.lazy(() => import('./pages/admin/Users'))
const AdminSystem = React.lazy(() => import('./pages/admin/System'))
const AdminLogs = React.lazy(() => import('./pages/admin/Logs'))
const NotFound = React.lazy(() => import('./pages/NotFound'))

// 加载组件
const PageLoading: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Spin size="large" tip="加载中..." />
  </div>
)

// 路由配置
const AppRoutes: React.FC = () => {
  return (
    <React.Suspense fallback={<PageLoading />}>
      <Routes>
        {/* 认证相关路由 */}
        <Route path="/auth" element={<AuthLayout />}>
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          <Route path="forgot-password" element={<ForgotPassword />} />
          <Route path="reset-password" element={<ResetPassword />} />
          <Route index element={<Navigate to="/auth/login" replace />} />
        </Route>

        {/* 主应用路由 */}
        <Route path="/" element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }>
          {/* 仪表板 */}
          <Route index element={<Dashboard />} />
          
          {/* USDT监控 */}
          <Route path="usdt">
            <Route path="monitor" element={<USDTMonitor />} />
            <Route path="history" element={<USDTHistory />} />
            <Route path="alerts" element={<USDTAlerts />} />
            <Route path="export" element={<USDTExport />} />
            <Route index element={<Navigate to="/usdt/monitor" replace />} />
          </Route>
          
          {/* 用户管理 */}
          <Route path="user">
            <Route path="profile" element={<UserProfile />} />
            <Route path="settings" element={<UserSettings />} />
            <Route index element={<Navigate to="/user/profile" replace />} />
          </Route>
          
          {/* 管理员功能 */}
          <Route path="admin">
            <Route path="users" element={
              <ProtectedRoute requireAdmin>
                <AdminUsers />
              </ProtectedRoute>
            } />
            <Route path="system" element={
              <ProtectedRoute requireAdmin>
                <AdminSystem />
              </ProtectedRoute>
            } />
            <Route path="logs" element={
              <ProtectedRoute requireAdmin>
                <AdminLogs />
              </ProtectedRoute>
            } />
            <Route index element={<Navigate to="/admin/users" replace />} />
          </Route>
        </Route>

        {/* 404页面 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </React.Suspense>
  )
}

const App: React.FC = () => {
  const { initializeAuth, isInitialized } = useAuthStore()
  const { initializeTheme } = useThemeStore()

  useEffect(() => {
    // 初始化认证状态
    initializeAuth()
    // 初始化主题
    initializeTheme()
  }, [initializeAuth, initializeTheme])

  // 等待初始化完成
  if (!isInitialized) {
    return <PageLoading />
  }

  return (
    <Layout className="min-h-screen">
      <AppRoutes />
    </Layout>
  )
}

export default App
