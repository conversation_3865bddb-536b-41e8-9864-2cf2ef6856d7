import React from 'react'
import { Result, Button } from 'antd'
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary'

interface ErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Result
        status="500"
        title="应用程序错误"
        subTitle={
          <div className="space-y-2">
            <p>抱歉，应用程序遇到了一个错误。</p>
            <details className="text-left bg-gray-100 p-4 rounded text-sm">
              <summary className="cursor-pointer font-medium">错误详情</summary>
              <pre className="mt-2 whitespace-pre-wrap">{error.message}</pre>
              {error.stack && (
                <pre className="mt-2 text-xs text-gray-600 whitespace-pre-wrap">
                  {error.stack}
                </pre>
              )}
            </details>
          </div>
        }
        extra={
          <div className="space-x-2">
            <Button type="primary" onClick={resetErrorBoundary}>
              重试
            </Button>
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          </div>
        }
      />
    </div>
  )
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

export const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  fallback = ErrorFallback,
  onError,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // 发送错误到监控服务
    if (onError) {
      onError(error, errorInfo)
    }
    
    // 可以在这里集成Sentry等错误监控服务
    // Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  return (
    <ReactErrorBoundary
      FallbackComponent={fallback}
      onError={handleError}
      onReset={() => {
        // 重置应用状态
        window.location.reload()
      }}
    >
      {children}
    </ReactErrorBoundary>
  )
}
