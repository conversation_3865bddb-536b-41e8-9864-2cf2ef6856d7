import React from 'react'
import { Outlet } from 'react-router-dom'
import { Layout, Card, Typography, Space } from 'antd'
import { DollarCircleOutlined } from '@ant-design/icons'

const { Content } = Layout
const { Title, Text } = Typography

export const AuthLayout: React.FC = () => {
  return (
    <Layout className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <Content className="flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Logo和标题 */}
          <div className="text-center mb-8">
            <Space direction="vertical" size="small">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-full mb-4">
                <DollarCircleOutlined className="text-3xl text-white" />
              </div>
              <Title level={2} className="mb-0">
                USDT Monitor
              </Title>
              <Text type="secondary" className="text-base">
                专业的USDT价格监控平台
              </Text>
            </Space>
          </div>

          {/* 认证表单卡片 */}
          <Card
            className="shadow-lg border-0"
            bodyStyle={{ padding: '32px' }}
          >
            <Outlet />
          </Card>

          {/* 底部信息 */}
          <div className="text-center mt-8 text-gray-500 dark:text-gray-400">
            <Text type="secondary" className="text-sm">
              © 2024 USDT Monitor Platform. All rights reserved.
            </Text>
          </div>
        </div>
      </Content>
    </Layout>
  )
}
