"""
CoinGecko API客户端
"""
import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
import aiohttp
import structlog

from app.core.config import settings
from app.core.exceptions import AppException

logger = structlog.get_logger()


class CoinGeckoClient:
    """CoinGecko API客户端"""
    
    def __init__(self):
        self.base_url = settings.COINGECKO_API_URL
        self.api_key = settings.COINGECKO_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limit_delay = 1.0  # 秒，避免触发速率限制
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
    
    async def connect(self):
        """建立连接"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {
                'User-Agent': 'USDT-Monitor/2.0',
                'Accept': 'application/json'
            }
            
            if self.api_key:
                headers['X-CG-Pro-API-Key'] = self.api_key
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers
            )
    
    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发起API请求"""
        if not self.session:
            await self.connect()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            # 添加速率限制延迟
            await asyncio.sleep(self.rate_limit_delay)
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(
                        "CoinGecko API request successful",
                        endpoint=endpoint,
                        status=response.status
                    )
                    return data
                elif response.status == 429:
                    # 速率限制，增加延迟
                    self.rate_limit_delay = min(self.rate_limit_delay * 2, 10.0)
                    logger.warning(
                        "CoinGecko API rate limit hit",
                        endpoint=endpoint,
                        new_delay=self.rate_limit_delay
                    )
                    raise AppException(
                        error_code="RATE_LIMIT_EXCEEDED",
                        message="API请求频率过高，请稍后重试"
                    )
                else:
                    error_text = await response.text()
                    logger.error(
                        "CoinGecko API request failed",
                        endpoint=endpoint,
                        status=response.status,
                        error=error_text
                    )
                    raise AppException(
                        error_code="API_REQUEST_FAILED",
                        message=f"API请求失败: {response.status}",
                        details={"endpoint": endpoint, "error": error_text}
                    )
        
        except aiohttp.ClientError as e:
            logger.error(
                "CoinGecko API connection error",
                endpoint=endpoint,
                error=str(e)
            )
            raise AppException(
                error_code="API_CONNECTION_ERROR",
                message="API连接失败",
                details={"endpoint": endpoint, "error": str(e)}
            )
    
    async def get_usdt_data(self) -> Dict[str, Any]:
        """获取USDT当前数据"""
        try:
            params = {
                'ids': 'tether',
                'vs_currencies': 'usd',
                'include_market_cap': 'true',
                'include_24hr_vol': 'true',
                'include_24hr_change': 'true',
                'include_last_updated_at': 'true'
            }
            
            data = await self._make_request('simple/price', params)
            
            if 'tether' not in data:
                raise AppException(
                    error_code="USDT_DATA_NOT_FOUND",
                    message="未找到USDT数据"
                )
            
            usdt_data = data['tether']
            
            # 获取详细信息
            detailed_data = await self.get_usdt_detailed_data()
            
            # 合并数据
            result = {
                'current_price_usd': Decimal(str(usdt_data.get('usd', 0))),
                'market_cap_usd': usdt_data.get('usd_market_cap'),
                'volume_24h': usdt_data.get('usd_24h_vol'),
                'price_change_24h': usdt_data.get('usd_24h_change'),
                'last_updated': datetime.fromtimestamp(usdt_data.get('last_updated_at', 0)),
                'data_source': 'coingecko',
                'timestamp': datetime.utcnow(),
                'unix_timestamp': int(datetime.utcnow().timestamp())
            }
            
            # 添加详细数据
            if detailed_data:
                result.update(detailed_data)
            
            return result
            
        except AppException:
            raise
        except Exception as e:
            logger.error("Failed to get USDT data from CoinGecko", error=str(e))
            raise AppException(
                error_code="USDT_DATA_FETCH_ERROR",
                message="获取USDT数据失败",
                details={"error": str(e)}
            )
    
    async def get_usdt_detailed_data(self) -> Optional[Dict[str, Any]]:
        """获取USDT详细数据"""
        try:
            params = {
                'localization': 'false',
                'tickers': 'false',
                'market_data': 'true',
                'community_data': 'false',
                'developer_data': 'false',
                'sparkline': 'false'
            }
            
            data = await self._make_request('coins/tether', params)
            
            market_data = data.get('market_data', {})
            
            return {
                'name': data.get('name', 'Tether'),
                'symbol': data.get('symbol', 'USDT').upper(),
                'market_cap_rank': market_data.get('market_cap_rank'),
                'total_volume_usd': market_data.get('total_volume', {}).get('usd'),
                'circulating_supply': Decimal(str(market_data.get('circulating_supply', 0))),
                'total_supply': Decimal(str(market_data.get('total_supply', 0))),
                'max_supply': Decimal(str(market_data.get('max_supply', 0))) if market_data.get('max_supply') else None,
                
                # 价格变化数据
                'price_change_percentage_24h': market_data.get('price_change_percentage_24h'),
                'price_change_percentage_7d': market_data.get('price_change_percentage_7d'),
                'price_change_percentage_30d': market_data.get('price_change_percentage_30d'),
                
                # 市值变化数据
                'market_cap_change_24h': market_data.get('market_cap_change_24h', {}).get('usd'),
                'market_cap_change_percentage_24h': market_data.get('market_cap_change_percentage_24h'),
                
                # 历史价格数据
                'ath': Decimal(str(market_data.get('ath', {}).get('usd', 0))),
                'ath_date': self._parse_date(market_data.get('ath_date', {}).get('usd')),
                'atl': Decimal(str(market_data.get('atl', {}).get('usd', 0))),
                'atl_date': self._parse_date(market_data.get('atl_date', {}).get('usd')),
                
                # 数据质量
                'confidence_score': 0.9,  # CoinGecko数据质量较高
                'source_id': data.get('id', 'tether')
            }
            
        except Exception as e:
            logger.warning("Failed to get detailed USDT data", error=str(e))
            return None
    
    async def get_usdt_history(
        self,
        days: int = 1,
        interval: str = 'hourly'
    ) -> List[Dict[str, Any]]:
        """获取USDT历史数据"""
        try:
            params = {
                'vs_currency': 'usd',
                'days': str(days),
                'interval': interval
            }
            
            data = await self._make_request('coins/tether/market_chart', params)
            
            prices = data.get('prices', [])
            volumes = data.get('total_volumes', [])
            market_caps = data.get('market_caps', [])
            
            history = []
            for i, price_data in enumerate(prices):
                timestamp = datetime.fromtimestamp(price_data[0] / 1000)
                price = Decimal(str(price_data[1]))
                
                volume = volumes[i][1] if i < len(volumes) else None
                market_cap = market_caps[i][1] if i < len(market_caps) else None
                
                history.append({
                    'timestamp': timestamp,
                    'unix_timestamp': int(timestamp.timestamp()),
                    'current_price_usd': price,
                    'volume_24h': int(volume) if volume else None,
                    'market_cap_usd': int(market_cap) if market_cap else None,
                    'data_source': 'coingecko',
                    'source_id': 'tether'
                })
            
            return history
            
        except AppException:
            raise
        except Exception as e:
            logger.error("Failed to get USDT history from CoinGecko", error=str(e))
            raise AppException(
                error_code="USDT_HISTORY_FETCH_ERROR",
                message="获取USDT历史数据失败",
                details={"error": str(e)}
            )
    
    async def get_market_status(self) -> Dict[str, Any]:
        """获取市场状态"""
        try:
            data = await self._make_request('global')
            
            global_data = data.get('data', {})
            
            return {
                'active_cryptocurrencies': global_data.get('active_cryptocurrencies'),
                'total_market_cap_usd': global_data.get('total_market_cap', {}).get('usd'),
                'total_volume_24h_usd': global_data.get('total_volume', {}).get('usd'),
                'market_cap_percentage': global_data.get('market_cap_percentage', {}),
                'updated_at': datetime.utcnow()
            }
            
        except Exception as e:
            logger.warning("Failed to get market status", error=str(e))
            return {}
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            # CoinGecko返回ISO格式的日期
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            return None
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self._make_request('ping')
            return True
        except Exception:
            return False
