{"name": "usdt-monitor-frontend", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@ant-design/icons": "^5.2.6", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "antd": "^5.12.8", "axios": "^1.6.2", "classnames": "^2.3.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@hookform/resolvers": "^3.3.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.4", "yup": "^1.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}