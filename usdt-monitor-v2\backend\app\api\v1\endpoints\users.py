"""
用户管理API端点
"""
from typing import Any, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.api.deps import get_current_active_user, get_current_admin_user
from app.domain.schemas.common import ApiResponse, PaginatedResponse, PaginationParams
from app.domain.schemas.user import User, UserCreate, UserUpdate, UserProfile
from app.application.services.user_service import UserService

router = APIRouter()


@router.get("/", response_model=ApiResponse[PaginatedResponse[User]])
async def get_users(
    pagination: PaginationParams = Depends(),
    search: str = Query(None, description="搜索关键词"),
    is_active: bool = Query(None, description="是否激活"),
    role: str = Query(None, description="用户角色"),
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    获取用户列表（管理员权限）
    """
    try:
        users, total = await user_service.get_users(
            offset=pagination.offset,
            limit=pagination.page_size,
            search=search,
            is_active=is_active,
            role=role
        )
        
        paginated_response = PaginatedResponse.create(
            items=users,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        return ApiResponse(
            data=paginated_response,
            message="获取用户列表成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/me", response_model=ApiResponse[User])
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取当前用户信息
    """
    return ApiResponse(
        data=current_user,
        message="获取用户信息成功"
    )


@router.put("/me", response_model=ApiResponse[User])
async def update_current_user_profile(
    user_update: UserProfile,
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends()
) -> Any:
    """
    更新当前用户信息
    """
    try:
        updated_user = await user_service.update_user_profile(
            user_id=current_user.id,
            user_update=user_update
        )
        
        return ApiResponse(
            data=updated_user,
            message="用户信息更新成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户信息更新失败"
        )


@router.get("/{user_id}", response_model=ApiResponse[User])
async def get_user_by_id(
    user_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    根据ID获取用户信息（管理员权限）
    """
    try:
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return ApiResponse(
            data=user,
            message="获取用户信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.post("/", response_model=ApiResponse[User])
async def create_user(
    user_create: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    创建新用户（管理员权限）
    """
    try:
        # 检查邮箱是否已存在
        existing_user = await user_service.get_user_by_email(user_create.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被注册"
            )
        
        # 检查用户名是否已存在
        if user_create.username:
            existing_username = await user_service.get_user_by_username(user_create.username)
            if existing_username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该用户名已被使用"
                )
        
        user = await user_service.create_user(user_create)
        
        return ApiResponse(
            data=user,
            message="用户创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户创建失败"
        )


@router.put("/{user_id}", response_model=ApiResponse[User])
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    更新用户信息（管理员权限）
    """
    try:
        # 检查用户是否存在
        existing_user = await user_service.get_user_by_id(user_id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 如果更新邮箱，检查是否已被其他用户使用
        if user_update.email and user_update.email != existing_user.email:
            email_user = await user_service.get_user_by_email(user_update.email)
            if email_user and email_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该邮箱已被其他用户使用"
                )
        
        # 如果更新用户名，检查是否已被其他用户使用
        if user_update.username and user_update.username != existing_user.username:
            username_user = await user_service.get_user_by_username(user_update.username)
            if username_user and username_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该用户名已被其他用户使用"
                )
        
        updated_user = await user_service.update_user(user_id, user_update)
        
        return ApiResponse(
            data=updated_user,
            message="用户信息更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户信息更新失败"
        )


@router.delete("/{user_id}", response_model=ApiResponse[dict])
async def delete_user(
    user_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    删除用户（管理员权限）
    """
    try:
        # 检查用户是否存在
        existing_user = await user_service.get_user_by_id(user_id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 不能删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的账户"
            )
        
        await user_service.delete_user(user_id)
        
        return ApiResponse(
            data={"message": "用户删除成功"},
            message="用户删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户删除失败"
        )


@router.post("/{user_id}/activate", response_model=ApiResponse[User])
async def activate_user(
    user_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    激活用户（管理员权限）
    """
    try:
        user = await user_service.activate_user(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return ApiResponse(
            data=user,
            message="用户激活成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户激活失败"
        )


@router.post("/{user_id}/deactivate", response_model=ApiResponse[User])
async def deactivate_user(
    user_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    user_service: UserService = Depends()
) -> Any:
    """
    停用用户（管理员权限）
    """
    try:
        # 不能停用自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能停用自己的账户"
            )
        
        user = await user_service.deactivate_user(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return ApiResponse(
            data=user,
            message="用户停用成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户停用失败"
        )
