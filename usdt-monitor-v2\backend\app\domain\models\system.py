"""
系统相关领域模型
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON>olean, DateTime, Enum as SQLE<PERSON>, Integer, String, Text, func
from sqlalchemy.dialects.postgresql import JSON, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class AlertLevel(str, Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """告警状态枚举"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class SystemSettings(Base):
    """系统设置模型"""
    
    __tablename__ = "system_settings"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 设置信息
    key: Mapped[str] = mapped_column(
        String(100), 
        unique=True, 
        index=True, 
        nullable=False
    )
    value: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    category: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    
    # 数据类型和验证
    data_type: Mapped[str] = mapped_column(
        String(20), 
        default="string"
    )  # string, integer, float, boolean, json
    is_sensitive: Mapped[bool] = mapped_column(Boolean, default=False)
    is_readonly: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        onupdate=func.now()
    )
    
    # 更新者信息
    updated_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True))
    
    def __repr__(self) -> str:
        return f"<SystemSettings(key={self.key}, category={self.category})>"
    
    def get_typed_value(self):
        """获取类型化的值"""
        if self.value is None:
            return None
        
        if self.data_type == "integer":
            return int(self.value)
        elif self.data_type == "float":
            return float(self.value)
        elif self.data_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.data_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value


class Alert(Base):
    """告警模型"""
    
    __tablename__ = "alerts"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 告警基本信息
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    level: Mapped[AlertLevel] = mapped_column(
        SQLEnum(AlertLevel), 
        nullable=False,
        index=True
    )
    status: Mapped[AlertStatus] = mapped_column(
        SQLEnum(AlertStatus), 
        default=AlertStatus.ACTIVE,
        index=True
    )
    
    # 告警分类
    category: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    source: Mapped[str] = mapped_column(String(100), nullable=False)  # 告警来源
    
    # 关联信息
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        index=True
    )
    resource_type: Mapped[Optional[str]] = mapped_column(String(50))
    resource_id: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 告警数据
    data: Mapped[Optional[dict]] = mapped_column(JSON)  # 告警相关的数据
    threshold_value: Mapped[Optional[str]] = mapped_column(String(100))
    actual_value: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 处理信息
    acknowledged_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    acknowledged_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True))
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    resolved_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True))
    resolution_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # 通知信息
    notification_sent: Mapped[bool] = mapped_column(Boolean, default=False)
    notification_channels: Mapped[Optional[list]] = mapped_column(JSON)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False,
        index=True
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        onupdate=func.now()
    )
    
    # 关联关系
    user: Mapped[Optional["User"]] = relationship("User", back_populates="alerts")
    
    def __repr__(self) -> str:
        return f"<Alert(id={self.id}, title={self.title}, level={self.level})>"
    
    @property
    def is_active(self) -> bool:
        """检查告警是否处于活跃状态"""
        return self.status == AlertStatus.ACTIVE
    
    @property
    def is_critical(self) -> bool:
        """检查是否为严重告警"""
        return self.level == AlertLevel.CRITICAL
    
    def acknowledge(self, user_id: uuid.UUID, notes: Optional[str] = None) -> None:
        """确认告警"""
        self.status = AlertStatus.ACKNOWLEDGED
        self.acknowledged_at = datetime.utcnow()
        self.acknowledged_by = user_id
        if notes:
            self.resolution_notes = notes
    
    def resolve(self, user_id: uuid.UUID, notes: Optional[str] = None) -> None:
        """解决告警"""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.utcnow()
        self.resolved_by = user_id
        if notes:
            self.resolution_notes = notes


class SystemHealth(Base):
    """系统健康状态模型"""
    
    __tablename__ = "system_health"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 服务信息
    service_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    service_version: Mapped[Optional[str]] = mapped_column(String(50))
    
    # 健康状态
    status: Mapped[str] = mapped_column(
        String(20), 
        nullable=False,
        index=True
    )  # healthy, degraded, unhealthy
    
    # 性能指标
    response_time_ms: Mapped[Optional[int]] = mapped_column(Integer)
    cpu_usage_percent: Mapped[Optional[float]] = mapped_column()
    memory_usage_percent: Mapped[Optional[float]] = mapped_column()
    disk_usage_percent: Mapped[Optional[float]] = mapped_column()
    
    # 连接状态
    database_connected: Mapped[bool] = mapped_column(Boolean, default=True)
    redis_connected: Mapped[bool] = mapped_column(Boolean, default=True)
    external_apis_connected: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # 详细信息
    details: Mapped[Optional[dict]] = mapped_column(JSON)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    
    # 时间戳
    checked_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    def __repr__(self) -> str:
        return f"<SystemHealth(service={self.service_name}, status={self.status})>"
    
    @property
    def is_healthy(self) -> bool:
        """检查服务是否健康"""
        return self.status == "healthy"


class AuditLog(Base):
    """审计日志模型"""
    
    __tablename__ = "audit_logs"
    
    # 主键
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    
    # 用户信息
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        index=True
    )
    username: Mapped[Optional[str]] = mapped_column(String(50))
    
    # 操作信息
    action: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    resource_type: Mapped[str] = mapped_column(String(50), nullable=False)
    resource_id: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 请求信息
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    request_method: Mapped[Optional[str]] = mapped_column(String(10))
    request_path: Mapped[Optional[str]] = mapped_column(String(500))
    
    # 操作结果
    success: Mapped[bool] = mapped_column(Boolean, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    
    # 详细数据
    old_values: Mapped[Optional[dict]] = mapped_column(JSON)
    new_values: Mapped[Optional[dict]] = mapped_column(JSON)
    additional_data: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    def __repr__(self) -> str:
        return f"<AuditLog(id={self.id}, action={self.action}, user={self.username})>"
