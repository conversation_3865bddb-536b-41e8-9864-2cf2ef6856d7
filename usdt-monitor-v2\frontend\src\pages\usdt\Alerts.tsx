import React, { useState } from 'react'
import {
  Card,
  Typography,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
  Alert,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BellOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import { usdtApi } from '../../services/api/usdt'
import type { USDTAlert, USDTAlertCreate } from '../../types/usdt'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

// 告警类型配置
const ALERT_TYPES = [
  {
    value: 'price_above',
    label: '价格高于',
    icon: <RiseOutlined />,
    color: 'red',
    description: '当USDT价格高于设定值时触发'
  },
  {
    value: 'price_below',
    label: '价格低于',
    icon: <FallOutlined />,
    color: 'blue',
    description: '当USDT价格低于设定值时触发'
  },
  {
    value: 'volatility_high',
    label: '波动率过高',
    icon: <WarningOutlined />,
    color: 'orange',
    description: '当价格波动率超过设定值时触发'
  },
  {
    value: 'deviation_high',
    label: '偏离锚定',
    icon: <WarningOutlined />,
    color: 'purple',
    description: '当价格偏离1美元超过设定百分比时触发'
  }
]

export default function Alerts() {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingAlert, setEditingAlert] = useState<USDTAlert | null>(null)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 获取告警列表
  const {
    data: alertsData,
    isPending: isLoading
  } = useQuery({
    queryKey: ['usdt', 'alerts', pagination.current, pagination.pageSize],
    queryFn: () => usdtApi.getUserAlerts({
      page: pagination.current,
      size: pagination.pageSize
    })
  })

  // 创建告警
  const createAlertMutation = useMutation({
    mutationFn: (alertData: USDTAlertCreate) => usdtApi.createAlert(alertData),
    onSuccess: () => {
      message.success('告警创建成功')
      setIsModalVisible(false)
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['usdt', 'alerts'] })
    },
    onError: () => {
      message.error('告警创建失败')
    }
  })

  // 更新告警
  const updateAlertMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<USDTAlertCreate> }) =>
      usdtApi.updateAlert(id, data),
    onSuccess: () => {
      message.success('告警更新成功')
      setIsModalVisible(false)
      setEditingAlert(null)
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['usdt', 'alerts'] })
    },
    onError: () => {
      message.error('告警更新失败')
    }
  })

  // 删除告警
  const deleteAlertMutation = useMutation({
    mutationFn: (id: string) => usdtApi.deleteAlert(id),
    onSuccess: () => {
      message.success('告警删除成功')
      queryClient.invalidateQueries({ queryKey: ['usdt', 'alerts'] })
    },
    onError: () => {
      message.error('告警删除失败')
    }
  })

  // 切换告警状态
  const toggleAlertMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
      usdtApi.toggleAlert(id, isActive),
    onSuccess: () => {
      message.success('告警状态更新成功')
      queryClient.invalidateQueries({ queryKey: ['usdt', 'alerts'] })
    },
    onError: () => {
      message.error('告警状态更新失败')
    }
  })

  const alerts = alertsData?.data?.items || []
  const total = alertsData?.data?.total || 0

  // 获取告警类型配置
  const getAlertTypeConfig = (type: string) => {
    return ALERT_TYPES.find(t => t.value === type) || ALERT_TYPES[0]
  }

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    const alertData: USDTAlertCreate = {
      alert_type: values.alert_type,
      threshold_value: values.threshold_value,
      description: values.description,
      is_active: values.is_active ?? true
    }

    if (editingAlert) {
      updateAlertMutation.mutate({ id: editingAlert.id, data: alertData })
    } else {
      createAlertMutation.mutate(alertData)
    }
  }

  // 打开编辑模态框
  const handleEdit = (alert: USDTAlert) => {
    setEditingAlert(alert)
    form.setFieldsValue({
      alert_type: alert.alert_type,
      threshold_value: alert.threshold_value,
      description: alert.description,
      is_active: alert.is_active
    })
    setIsModalVisible(true)
  }

  // 关闭模态框
  const handleCancel = () => {
    setIsModalVisible(false)
    setEditingAlert(null)
    form.resetFields()
  }

  // 表格列定义
  const columns: ColumnsType<USDTAlert> = [
    {
      title: '告警类型',
      dataIndex: 'alert_type',
      key: 'alert_type',
      width: 150,
      render: (type: string) => {
        const config = getAlertTypeConfig(type)
        return (
          <Space>
            <span style={{ color: config.color }}>{config.icon}</span>
            <span>{config.label}</span>
          </Space>
        )
      }
    },
    {
      title: '阈值',
      dataIndex: 'threshold_value',
      key: 'threshold_value',
      width: 120,
      render: (value: number, record: USDTAlert) => {
        const config = getAlertTypeConfig(record.alert_type)
        const suffix = record.alert_type.includes('price') ? ' USD' :
                      record.alert_type === 'volatility_high' ? '%' : '%'
        return (
          <Text strong>
            {record.alert_type.includes('price') ? '$' : ''}{value.toFixed(6)}{suffix}
          </Text>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean, record: USDTAlert) => (
        <Switch
          checked={isActive}
          onChange={(checked) => toggleAlertMutation.mutate({
            id: record.id,
            isActive: checked
          })}
          loading={toggleAlertMutation.isPending}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '触发次数',
      dataIndex: 'triggered_count',
      key: 'triggered_count',
      width: 100,
      render: (count: number) => (
        <Badge count={count} showZero>
          <BellOutlined />
        </Badge>
      )
    },
    {
      title: '最后触发',
      dataIndex: 'last_triggered',
      key: 'last_triggered',
      width: 150,
      render: (time: string) => {
        if (!time) return <Text type="secondary">从未触发</Text>
        return (
          <div>
            <div>{dayjs(time).format('MM-DD HH:mm')}</div>
            <Text type="secondary" className="text-xs">
              {dayjs(time).fromNow()}
            </Text>
          </div>
        )
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('MM-DD HH:mm')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(time).fromNow()}
          </Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: USDTAlert) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个告警吗？"
              onConfirm={() => deleteAlertMutation.mutate(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                loading={deleteAlertMutation.isPending}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  // 计算统计数据
  const statistics = {
    total: alerts.length,
    active: alerts.filter(alert => alert.is_active).length,
    triggered: alerts.filter(alert => alert.triggered_count > 0).length,
    inactive: alerts.filter(alert => !alert.is_active).length
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="mb-2">
            价格告警管理
          </Title>
          <Text type="secondary">
            设置和管理USDT价格告警规则
          </Text>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          创建告警
        </Button>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="总告警数"
              value={statistics.total}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="启用中"
              value={statistics.active}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="已触发"
              value={statistics.triggered}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="已禁用"
              value={statistics.inactive}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 告警类型说明 */}
      <Card title="告警类型说明" size="small">
        <Row gutter={[16, 16]}>
          {ALERT_TYPES.map(type => (
            <Col xs={24} sm={12} lg={6} key={type.value}>
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <span style={{ color: type.color }}>{type.icon}</span>
                  <Text strong>{type.label}</Text>
                </div>
                <Text type="secondary" className="text-sm">
                  {type.description}
                </Text>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 告警列表 */}
      <Card title="告警列表">
        <Table
          columns={columns}
          dataSource={alerts}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination({ current: page, pageSize: pageSize || 20, total })
            }
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 创建/编辑告警模态框 */}
      <Modal
        title={editingAlert ? '编辑告警' : '创建告警'}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            is_active: true
          }}
        >
          <Form.Item
            name="alert_type"
            label="告警类型"
            rules={[{ required: true, message: '请选择告警类型' }]}
          >
            <Select placeholder="选择告警类型">
              {ALERT_TYPES.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    <span style={{ color: type.color }}>{type.icon}</span>
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="threshold_value"
            label="阈值"
            rules={[
              { required: true, message: '请输入阈值' },
              { type: 'number', min: 0, message: '阈值必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              precision={6}
              placeholder="请输入阈值"
              addonBefore="$"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="可选：添加告警描述"
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createAlertMutation.isPending || updateAlertMutation.isPending}
              >
                {editingAlert ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
