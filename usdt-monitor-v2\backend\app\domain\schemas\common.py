"""
通用的Pydantic schemas
"""
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """API响应基础schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[T] = None
    meta: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """错误响应schema"""
    success: bool = False
    error_code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None


class PaginationParams(BaseModel):
    """分页参数schema"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应schema"""
    items: List[T]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    
    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        page: int,
        page_size: int,
    ) -> "PaginatedResponse[T]":
        """创建分页响应"""
        total_pages = (total + page_size - 1) // page_size
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1,
        )


class SortParams(BaseModel):
    """排序参数schema"""
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: Optional[str] = Field(
        default="asc", 
        regex="^(asc|desc)$", 
        description="排序方向"
    )


class FilterParams(BaseModel):
    """过滤参数schema"""
    search: Optional[str] = Field(default=None, description="搜索关键词")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="过滤条件")


class HealthCheck(BaseModel):
    """健康检查schema"""
    status: str
    version: str
    environment: str
    timestamp: float
    checks: Optional[Dict[str, str]] = None


class MetricsResponse(BaseModel):
    """指标响应schema"""
    metrics: Dict[str, Any]
    timestamp: float


class BulkOperationRequest(BaseModel):
    """批量操作请求schema"""
    ids: List[str] = Field(..., min_items=1, max_items=100)
    operation: str = Field(..., min_length=1)
    parameters: Optional[Dict[str, Any]] = None


class BulkOperationResponse(BaseModel):
    """批量操作响应schema"""
    success_count: int
    failure_count: int
    total_count: int
    errors: Optional[List[Dict[str, Any]]] = None


class FileUploadResponse(BaseModel):
    """文件上传响应schema"""
    filename: str
    file_size: int
    file_type: str
    file_url: str
    upload_id: str


class ExportRequest(BaseModel):
    """导出请求schema"""
    format: str = Field(..., regex="^(csv|xlsx|json)$")
    filters: Optional[Dict[str, Any]] = None
    fields: Optional[List[str]] = None


class ExportResponse(BaseModel):
    """导出响应schema"""
    export_id: str
    status: str
    download_url: Optional[str] = None
    expires_at: Optional[str] = None


class ImportRequest(BaseModel):
    """导入请求schema"""
    file_url: str
    format: str = Field(..., regex="^(csv|xlsx|json)$")
    options: Optional[Dict[str, Any]] = None


class ImportResponse(BaseModel):
    """导入响应schema"""
    import_id: str
    status: str
    total_records: Optional[int] = None
    processed_records: Optional[int] = None
    success_records: Optional[int] = None
    error_records: Optional[int] = None
    errors: Optional[List[Dict[str, Any]]] = None


class ValidationErrorDetail(BaseModel):
    """验证错误详情schema"""
    field: str
    message: str
    value: Any


class ValidationErrorResponse(BaseModel):
    """验证错误响应schema"""
    success: bool = False
    error_code: str = "VALIDATION_ERROR"
    message: str = "验证失败"
    errors: List[ValidationErrorDetail]


class CacheStats(BaseModel):
    """缓存统计schema"""
    hit_rate: float
    miss_rate: float
    total_keys: int
    memory_usage: int
    connections: int


class DatabaseStats(BaseModel):
    """数据库统计schema"""
    total_connections: int
    active_connections: int
    idle_connections: int
    total_queries: int
    slow_queries: int
    database_size: int


class SystemStats(BaseModel):
    """系统统计schema"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    uptime: int
